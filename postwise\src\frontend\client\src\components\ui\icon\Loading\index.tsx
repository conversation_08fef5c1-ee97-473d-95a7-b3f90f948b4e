import React, { forwardRef } from "react";
import { cn } from "~/utils";

export const LoadingIcon = forwardRef<
    SVGSVGElement & { className: any },
    React.PropsWithChildren<{ className?: string }>
>(({ className, ...props }, ref) => {
    // return <svg ref={ref} {...props} className={cn('text-primary', className)} viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg" width="80" height="80">
    //     <rect x="10" y="8" width="3" height="2" rx="1" fill="currentColor"></rect>
    //     <rect x="14" y="8" width="10" height="2" rx="1" fill="currentColor">
    //         <animate attributeName="width" values="12;7;12" dur="1s" repeatCount="indefinite" begin="-0.3s" />
    //     </rect>
    //     <rect x="11" y="11.5" width="16" height="2" rx="1" fill="currentColor">
    //         <animate attributeName="width" values="18;14;18" dur="1s" repeatCount="indefinite" begin="-0.6s" />
    //     </rect>
    //     <rect x="11" y="15" width="12" height="2" rx="1" fill="currentColor">
    //         <animate attributeName="width" values="14;10;14" dur="1s" repeatCount="indefinite" begin="-0.9s" />
    //     </rect>
    //     <rect x="11" y="18.5" width="14" height="2" rx="1" fill="currentColor">
    //         <animate attributeName="width" values="16;12;16" dur="1s" repeatCount="indefinite" begin="-0.2s" />
    //     </rect>
    //     <rect x="11" y="22" width="16" height="2" rx="1" fill="currentColor">
    //         <animate attributeName="width" values="18;14;18" dur="1s" repeatCount="indefinite" begin="-1.5s" />
    //     </rect>
    //     <rect x="10" y="25.5" width="10" height="2" rx="1" fill="currentColor">
    //         <animate attributeName="width" values="12;9;12" dur="1s" repeatCount="indefinite" begin="-0.1s" />
    //     </rect>
    //     <rect x="21" y="25.5" width="3" height="2" rx="1" fill="currentColor">
    //         <animate attributeName="x" values="23;20;23" dur="1s" repeatCount="indefinite" begin="-0.1s" />
    //     </rect>
    // </svg>

    return <svg ref={ref} {...props} className={cn('text-primary', className)} viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg" width="80" height="80">
    <path d="M10 8v20h4V19h6c3.3 0 6-2.7 6-5.5S23.3 8 20 8H10z" stroke="currentColor" stroke-width="2" fill="none">
        <animate attributeName="stroke-dasharray" values="0,100;100,0" dur="1.2s" repeatCount="indefinite" />
    </path>
    
    <path d="M14 12h5c1.7 0 3 1.3 3 3s-1.3 3-3 3h-5v-6z" fill="none" stroke="currentColor" stroke-width="1.5">
        <animate attributeName="stroke-dasharray" values="0,60;60,0" dur="1.2s" begin="0.4s" repeatCount="indefinite" />
    </path>
    
    <circle cx="22" cy="11" r="1.5" fill="currentColor">
        <animate attributeName="opacity" values="0.3;1;0.3" dur="1s" repeatCount="indefinite" begin="0.2s" />
    </circle>
</svg>
});