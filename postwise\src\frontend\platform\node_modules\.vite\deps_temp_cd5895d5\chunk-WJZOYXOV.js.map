{"version": 3, "sources": ["../../highlight.js/lib/languages/gauss.js"], "sourcesContent": ["/*\nLanguage: GAUSS\nAuthor: <PERSON> <<EMAIL>>\nDescription: GAUSS Mathematical and Statistical language\nWebsite: https://www.aptech.com\nCategory: scientific\n*/\nfunction gauss(hljs) {\n  const KEYWORDS = {\n    keyword: 'bool break call callexe checkinterrupt clear clearg closeall cls comlog compile ' +\n              'continue create debug declare delete disable dlibrary dllcall do dos ed edit else ' +\n              'elseif enable end endfor endif endp endo errorlog errorlogat expr external fn ' +\n              'for format goto gosub graph if keyword let lib library line load loadarray loadexe ' +\n              'loadf loadk loadm loadp loads loadx local locate loopnextindex lprint lpwidth lshow ' +\n              'matrix msym ndpclex new open output outwidth plot plotsym pop prcsn print ' +\n              'printdos proc push retp return rndcon rndmod rndmult rndseed run save saveall screen ' +\n              'scroll setarray show sparse stop string struct system trace trap threadfor ' +\n              'threadendfor threadbegin threadjoin threadstat threadend until use while winprint ' +\n              'ne ge le gt lt and xor or not eq eqv',\n    built_in: 'abs acf aconcat aeye amax amean AmericanBinomCall AmericanBinomCall_Greeks AmericanBinomCall_ImpVol ' +\n              'AmericanBinomPut AmericanBinomPut_Greeks AmericanBinomPut_ImpVol AmericanBSCall AmericanBSCall_Greeks ' +\n              'AmericanBSCall_ImpVol AmericanBSPut AmericanBSPut_Greeks AmericanBSPut_ImpVol amin amult annotationGetDefaults ' +\n              'annotationSetBkd annotationSetFont annotationSetLineColor annotationSetLineStyle annotationSetLineThickness ' +\n              'annualTradingDays arccos arcsin areshape arrayalloc arrayindex arrayinit arraytomat asciiload asclabel astd ' +\n              'astds asum atan atan2 atranspose axmargin balance band bandchol bandcholsol bandltsol bandrv bandsolpd bar ' +\n              'base10 begwind besselj bessely beta box boxcox cdfBeta cdfBetaInv cdfBinomial cdfBinomialInv cdfBvn cdfBvn2 ' +\n              'cdfBvn2e cdfCauchy cdfCauchyInv cdfChic cdfChii cdfChinc cdfChincInv cdfExp cdfExpInv cdfFc cdfFnc cdfFncInv ' +\n              'cdfGam cdfGenPareto cdfHyperGeo cdfLaplace cdfLaplaceInv cdfLogistic cdfLogisticInv cdfmControlCreate cdfMvn ' +\n              'cdfMvn2e cdfMvnce cdfMvne cdfMvt2e cdfMvtce cdfMvte cdfN cdfN2 cdfNc cdfNegBinomial cdfNegBinomialInv cdfNi ' +\n              'cdfPoisson cdfPoissonInv cdfRayleigh cdfRayleighInv cdfTc cdfTci cdfTnc cdfTvn cdfWeibull cdfWeibullInv cdir ' +\n              'ceil ChangeDir chdir chiBarSquare chol choldn cholsol cholup chrs close code cols colsf combinate combinated ' +\n              'complex con cond conj cons ConScore contour conv convertsatostr convertstrtosa corrm corrms corrvc corrx corrxs ' +\n              'cos cosh counts countwts crossprd crout croutp csrcol csrlin csvReadM csvReadSA cumprodc cumsumc curve cvtos ' +\n              'datacreate datacreatecomplex datalist dataload dataloop dataopen datasave date datestr datestring datestrymd ' +\n              'dayinyr dayofweek dbAddDatabase dbClose dbCommit dbCreateQuery dbExecQuery dbGetConnectOptions dbGetDatabaseName ' +\n              'dbGetDriverName dbGetDrivers dbGetHostName dbGetLastErrorNum dbGetLastErrorText dbGetNumericalPrecPolicy ' +\n              'dbGetPassword dbGetPort dbGetTableHeaders dbGetTables dbGetUserName dbHasFeature dbIsDriverAvailable dbIsOpen ' +\n              'dbIsOpenError dbOpen dbQueryBindValue dbQueryClear dbQueryCols dbQueryExecPrepared dbQueryFetchAllM dbQueryFetchAllSA ' +\n              'dbQueryFetchOneM dbQueryFetchOneSA dbQueryFinish dbQueryGetBoundValue dbQueryGetBoundValues dbQueryGetField ' +\n              'dbQueryGetLastErrorNum dbQueryGetLastErrorText dbQueryGetLastInsertID dbQueryGetLastQuery dbQueryGetPosition ' +\n              'dbQueryIsActive dbQueryIsForwardOnly dbQueryIsNull dbQueryIsSelect dbQueryIsValid dbQueryPrepare dbQueryRows ' +\n              'dbQuerySeek dbQuerySeekFirst dbQuerySeekLast dbQuerySeekNext dbQuerySeekPrevious dbQuerySetForwardOnly ' +\n              'dbRemoveDatabase dbRollback dbSetConnectOptions dbSetDatabaseName dbSetHostName dbSetNumericalPrecPolicy ' +\n              'dbSetPort dbSetUserName dbTransaction DeleteFile delif delrows denseToSp denseToSpRE denToZero design det detl ' +\n              'dfft dffti diag diagrv digamma doswin DOSWinCloseall DOSWinOpen dotfeq dotfeqmt dotfge dotfgemt dotfgt dotfgtmt ' +\n              'dotfle dotflemt dotflt dotfltmt dotfne dotfnemt draw drop dsCreate dstat dstatmt dstatmtControlCreate dtdate dtday ' +\n              'dttime dttodtv dttostr dttoutc dtvnormal dtvtodt dtvtoutc dummy dummybr dummydn eig eigh eighv eigv elapsedTradingDays ' +\n              'endwind envget eof eqSolve eqSolvemt eqSolvemtControlCreate eqSolvemtOutCreate eqSolveset erf erfc erfccplx erfcplx error ' +\n              'etdays ethsec etstr EuropeanBinomCall EuropeanBinomCall_Greeks EuropeanBinomCall_ImpVol EuropeanBinomPut ' +\n              'EuropeanBinomPut_Greeks EuropeanBinomPut_ImpVol EuropeanBSCall EuropeanBSCall_Greeks EuropeanBSCall_ImpVol ' +\n              'EuropeanBSPut EuropeanBSPut_Greeks EuropeanBSPut_ImpVol exctsmpl exec execbg exp extern eye fcheckerr fclearerr feq ' +\n              'feqmt fflush fft ffti fftm fftmi fftn fge fgemt fgets fgetsa fgetsat fgetst fgt fgtmt fileinfo filesa fle flemt ' +\n              'floor flt fltmt fmod fne fnemt fonts fopen formatcv formatnv fputs fputst fseek fstrerror ftell ftocv ftos ftostrC ' +\n              'gamma gammacplx gammaii gausset gdaAppend gdaCreate gdaDStat gdaDStatMat gdaGetIndex gdaGetName gdaGetNames gdaGetOrders ' +\n              'gdaGetType gdaGetTypes gdaGetVarInfo gdaIsCplx gdaLoad gdaPack gdaRead gdaReadByIndex gdaReadSome gdaReadSparse ' +\n              'gdaReadStruct gdaReportVarInfo gdaSave gdaUpdate gdaUpdateAndPack gdaVars gdaWrite gdaWrite32 gdaWriteSome getarray ' +\n              'getdims getf getGAUSShome getmatrix getmatrix4D getname getnamef getNextTradingDay getNextWeekDay getnr getorders ' +\n              'getpath getPreviousTradingDay getPreviousWeekDay getRow getscalar3D getscalar4D getTrRow getwind glm gradcplx gradMT ' +\n              'gradMTm gradMTT gradMTTm gradp graphprt graphset hasimag header headermt hess hessMT hessMTg hessMTgw hessMTm ' +\n              'hessMTmw hessMTT hessMTTg hessMTTgw hessMTTm hessMTw hessp hist histf histp hsec imag indcv indexcat indices indices2 ' +\n              'indicesf indicesfn indnv indsav integrate1d integrateControlCreate intgrat2 intgrat3 inthp1 inthp2 inthp3 inthp4 ' +\n              'inthpControlCreate intquad1 intquad2 intquad3 intrleav intrleavsa intrsect intsimp inv invpd invswp iscplx iscplxf ' +\n              'isden isinfnanmiss ismiss key keyav keyw lag lag1 lagn lapEighb lapEighi lapEighvb lapEighvi lapgEig lapgEigh lapgEighv ' +\n              'lapgEigv lapgSchur lapgSvdcst lapgSvds lapgSvdst lapSvdcusv lapSvds lapSvdusv ldlp ldlsol linSolve listwise ln lncdfbvn ' +\n              'lncdfbvn2 lncdfmvn lncdfn lncdfn2 lncdfnc lnfact lngammacplx lnpdfmvn lnpdfmvt lnpdfn lnpdft loadd loadstruct loadwind ' +\n              'loess loessmt loessmtControlCreate log loglog logx logy lower lowmat lowmat1 ltrisol lu lusol machEpsilon make makevars ' +\n              'makewind margin matalloc matinit mattoarray maxbytes maxc maxindc maxv maxvec mbesselei mbesselei0 mbesselei1 mbesseli ' +\n              'mbesseli0 mbesseli1 meanc median mergeby mergevar minc minindc minv miss missex missrv moment momentd movingave ' +\n              'movingaveExpwgt movingaveWgt nextindex nextn nextnevn nextwind ntos null null1 numCombinations ols olsmt olsmtControlCreate ' +\n              'olsqr olsqr2 olsqrmt ones optn optnevn orth outtyp pacf packedToSp packr parse pause pdfCauchy pdfChi pdfExp pdfGenPareto ' +\n              'pdfHyperGeo pdfLaplace pdfLogistic pdfn pdfPoisson pdfRayleigh pdfWeibull pi pinv pinvmt plotAddArrow plotAddBar plotAddBox ' +\n              'plotAddHist plotAddHistF plotAddHistP plotAddPolar plotAddScatter plotAddShape plotAddTextbox plotAddTS plotAddXY plotArea ' +\n              'plotBar plotBox plotClearLayout plotContour plotCustomLayout plotGetDefaults plotHist plotHistF plotHistP plotLayout ' +\n              'plotLogLog plotLogX plotLogY plotOpenWindow plotPolar plotSave plotScatter plotSetAxesPen plotSetBar plotSetBarFill ' +\n              'plotSetBarStacked plotSetBkdColor plotSetFill plotSetGrid plotSetLegend plotSetLineColor plotSetLineStyle plotSetLineSymbol ' +\n              'plotSetLineThickness plotSetNewWindow plotSetTitle plotSetWhichYAxis plotSetXAxisShow plotSetXLabel plotSetXRange ' +\n              'plotSetXTicInterval plotSetXTicLabel plotSetYAxisShow plotSetYLabel plotSetYRange plotSetZAxisShow plotSetZLabel ' +\n              'plotSurface plotTS plotXY polar polychar polyeval polygamma polyint polymake polymat polymroot polymult polyroot ' +\n              'pqgwin previousindex princomp printfm printfmt prodc psi putarray putf putvals pvCreate pvGetIndex pvGetParNames ' +\n              'pvGetParVector pvLength pvList pvPack pvPacki pvPackm pvPackmi pvPacks pvPacksi pvPacksm pvPacksmi pvPutParVector ' +\n              'pvTest pvUnpack QNewton QNewtonmt QNewtonmtControlCreate QNewtonmtOutCreate QNewtonSet QProg QProgmt QProgmtInCreate ' +\n              'qqr qqre qqrep qr qre qrep qrsol qrtsol qtyr qtyre qtyrep quantile quantiled qyr qyre qyrep qz rank rankindx readr ' +\n              'real reclassify reclassifyCuts recode recserar recsercp recserrc rerun rescale reshape rets rev rfft rffti rfftip rfftn ' +\n              'rfftnp rfftp rndBernoulli rndBeta rndBinomial rndCauchy rndChiSquare rndCon rndCreateState rndExp rndGamma rndGeo rndGumbel ' +\n              'rndHyperGeo rndi rndKMbeta rndKMgam rndKMi rndKMn rndKMnb rndKMp rndKMu rndKMvm rndLaplace rndLCbeta rndLCgam rndLCi rndLCn ' +\n              'rndLCnb rndLCp rndLCu rndLCvm rndLogNorm rndMTu rndMVn rndMVt rndn rndnb rndNegBinomial rndp rndPoisson rndRayleigh ' +\n              'rndStateSkip rndu rndvm rndWeibull rndWishart rotater round rows rowsf rref sampleData satostrC saved saveStruct savewind ' +\n              'scale scale3d scalerr scalinfnanmiss scalmiss schtoc schur searchsourcepath seekr select selif seqa seqm setdif setdifsa ' +\n              'setvars setvwrmode setwind shell shiftr sin singleindex sinh sleep solpd sortc sortcc sortd sorthc sorthcc sortind ' +\n              'sortindc sortmc sortr sortrc spBiconjGradSol spChol spConjGradSol spCreate spDenseSubmat spDiagRvMat spEigv spEye spLDL ' +\n              'spline spLU spNumNZE spOnes spreadSheetReadM spreadSheetReadSA spreadSheetWrite spScale spSubmat spToDense spTrTDense ' +\n              'spTScalar spZeros sqpSolve sqpSolveMT sqpSolveMTControlCreate sqpSolveMTlagrangeCreate sqpSolveMToutCreate sqpSolveSet ' +\n              'sqrt statements stdc stdsc stocv stof strcombine strindx strlen strput strrindx strsect strsplit strsplitPad strtodt ' +\n              'strtof strtofcplx strtriml strtrimr strtrunc strtruncl strtruncpad strtruncr submat subscat substute subvec sumc sumr ' +\n              'surface svd svd1 svd2 svdcusv svds svdusv sysstate tab tan tanh tempname ' +\n              'time timedt timestr timeutc title tkf2eps tkf2ps tocart todaydt toeplitz token topolar trapchk ' +\n              'trigamma trimr trunc type typecv typef union unionsa uniqindx uniqindxsa unique uniquesa upmat upmat1 upper utctodt ' +\n              'utctodtv utrisol vals varCovMS varCovXS varget vargetl varmall varmares varput varputl vartypef vcm vcms vcx vcxs ' +\n              'vec vech vecr vector vget view viewxyz vlist vnamecv volume vput vread vtypecv wait waitc walkindex where window ' +\n              'writer xlabel xlsGetSheetCount xlsGetSheetSize xlsGetSheetTypes xlsMakeRange xlsReadM xlsReadSA xlsWrite xlsWriteM ' +\n              'xlsWriteSA xpnd xtics xy xyz ylabel ytics zeros zeta zlabel ztics cdfEmpirical dot h5create h5open h5read h5readAttribute ' +\n              'h5write h5writeAttribute ldl plotAddErrorBar plotAddSurface plotCDFEmpirical plotSetColormap plotSetContourLabels ' +\n              'plotSetLegendFont plotSetTextInterpreter plotSetXTicCount plotSetYTicCount plotSetZLevels powerm strjoin sylvester ' +\n              'strtrim',\n    literal: 'DB_AFTER_LAST_ROW DB_ALL_TABLES DB_BATCH_OPERATIONS DB_BEFORE_FIRST_ROW DB_BLOB DB_EVENT_NOTIFICATIONS ' +\n             'DB_FINISH_QUERY DB_HIGH_PRECISION DB_LAST_INSERT_ID DB_LOW_PRECISION_DOUBLE DB_LOW_PRECISION_INT32 ' +\n             'DB_LOW_PRECISION_INT64 DB_LOW_PRECISION_NUMBERS DB_MULTIPLE_RESULT_SETS DB_NAMED_PLACEHOLDERS ' +\n             'DB_POSITIONAL_PLACEHOLDERS DB_PREPARED_QUERIES DB_QUERY_SIZE DB_SIMPLE_LOCKING DB_SYSTEM_TABLES DB_TABLES ' +\n             'DB_TRANSACTIONS DB_UNICODE DB_VIEWS __STDIN __STDOUT __STDERR __FILE_DIR'\n  };\n\n  const AT_COMMENT_MODE = hljs.COMMENT('@', '@');\n\n  const PREPROCESSOR =\n  {\n    className: 'meta',\n    begin: '#',\n    end: '$',\n    keywords: {\n      'meta-keyword': 'define definecs|10 undef ifdef ifndef iflight ifdllcall ifmac ifos2win ifunix else endif lineson linesoff srcfile srcline'\n    },\n    contains: [\n      {\n        begin: /\\\\\\n/,\n        relevance: 0\n      },\n      {\n        beginKeywords: 'include',\n        end: '$',\n        keywords: {\n          'meta-keyword': 'include'\n        },\n        contains: [\n          {\n            className: 'meta-string',\n            begin: '\"',\n            end: '\"',\n            illegal: '\\\\n'\n          }\n        ]\n      },\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      AT_COMMENT_MODE\n    ]\n  };\n\n  const STRUCT_TYPE =\n  {\n    begin: /\\bstruct\\s+/,\n    end: /\\s/,\n    keywords: \"struct\",\n    contains: [\n      {\n        className: \"type\",\n        begin: hljs.UNDERSCORE_IDENT_RE,\n        relevance: 0\n      }\n    ]\n  };\n\n  // only for definitions\n  const PARSE_PARAMS = [\n    {\n      className: 'params',\n      begin: /\\(/,\n      end: /\\)/,\n      excludeBegin: true,\n      excludeEnd: true,\n      endsWithParent: true,\n      relevance: 0,\n      contains: [\n        { // dots\n          className: 'literal',\n          begin: /\\.\\.\\./\n        },\n        hljs.C_NUMBER_MODE,\n        hljs.C_BLOCK_COMMENT_MODE,\n        AT_COMMENT_MODE,\n        STRUCT_TYPE\n      ]\n    }\n  ];\n\n  const FUNCTION_DEF =\n  {\n    className: \"title\",\n    begin: hljs.UNDERSCORE_IDENT_RE,\n    relevance: 0\n  };\n\n  const DEFINITION = function(beginKeywords, end, inherits) {\n    const mode = hljs.inherit(\n      {\n        className: \"function\",\n        beginKeywords: beginKeywords,\n        end: end,\n        excludeEnd: true,\n        contains: [].concat(PARSE_PARAMS)\n      },\n      inherits || {}\n    );\n    mode.contains.push(FUNCTION_DEF);\n    mode.contains.push(hljs.C_NUMBER_MODE);\n    mode.contains.push(hljs.C_BLOCK_COMMENT_MODE);\n    mode.contains.push(AT_COMMENT_MODE);\n    return mode;\n  };\n\n  const BUILT_IN_REF =\n  { // these are explicitly named internal function calls\n    className: 'built_in',\n    begin: '\\\\b(' + KEYWORDS.built_in.split(' ').join('|') + ')\\\\b'\n  };\n\n  const STRING_REF =\n  {\n    className: 'string',\n    begin: '\"',\n    end: '\"',\n    contains: [hljs.BACKSLASH_ESCAPE],\n    relevance: 0\n  };\n\n  const FUNCTION_REF =\n  {\n    // className: \"fn_ref\",\n    begin: hljs.UNDERSCORE_IDENT_RE + '\\\\s*\\\\(',\n    returnBegin: true,\n    keywords: KEYWORDS,\n    relevance: 0,\n    contains: [\n      {\n        beginKeywords: KEYWORDS.keyword\n      },\n      BUILT_IN_REF,\n      { // ambiguously named function calls get a relevance of 0\n        className: 'built_in',\n        begin: hljs.UNDERSCORE_IDENT_RE,\n        relevance: 0\n      }\n    ]\n  };\n\n  const FUNCTION_REF_PARAMS =\n  {\n    // className: \"fn_ref_params\",\n    begin: /\\(/,\n    end: /\\)/,\n    relevance: 0,\n    keywords: {\n      built_in: KEYWORDS.built_in,\n      literal: KEYWORDS.literal\n    },\n    contains: [\n      hljs.C_NUMBER_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      AT_COMMENT_MODE,\n      BUILT_IN_REF,\n      FUNCTION_REF,\n      STRING_REF,\n      'self'\n    ]\n  };\n\n  FUNCTION_REF.contains.push(FUNCTION_REF_PARAMS);\n\n  return {\n    name: 'GAUSS',\n    aliases: ['gss'],\n    case_insensitive: true, // language is case-insensitive\n    keywords: KEYWORDS,\n    illegal: /(\\{[%#]|[%#]\\}| <- )/,\n    contains: [\n      hljs.C_NUMBER_MODE,\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      AT_COMMENT_MODE,\n      STRING_REF,\n      PREPROCESSOR,\n      {\n        className: 'keyword',\n        begin: /\\bexternal (matrix|string|array|sparse matrix|struct|proc|keyword|fn)/\n      },\n      DEFINITION('proc keyword', ';'),\n      DEFINITION('fn', '='),\n      {\n        beginKeywords: 'for threadfor',\n        end: /;/,\n        // end: /\\(/,\n        relevance: 0,\n        contains: [\n          hljs.C_BLOCK_COMMENT_MODE,\n          AT_COMMENT_MODE,\n          FUNCTION_REF_PARAMS\n        ]\n      },\n      { // custom method guard\n        // excludes method names from keyword processing\n        variants: [\n          {\n            begin: hljs.UNDERSCORE_IDENT_RE + '\\\\.' + hljs.UNDERSCORE_IDENT_RE\n          },\n          {\n            begin: hljs.UNDERSCORE_IDENT_RE + '\\\\s*='\n          }\n        ],\n        relevance: 0\n      },\n      FUNCTION_REF,\n      STRUCT_TYPE\n    ]\n  };\n}\n\nmodule.exports = gauss;\n"], "mappings": ";;;;;AAAA;AAAA;AAOA,aAAS,MAAM,MAAM;AACnB,YAAM,WAAW;AAAA,QACf,SAAS;AAAA,QAUT,UAAU;AAAA,QAqFV,SAAS;AAAA,MAKX;AAEA,YAAM,kBAAkB,KAAK,QAAQ,KAAK,GAAG;AAE7C,YAAM,eACN;AAAA,QACE,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,UACR,gBAAgB;AAAA,QAClB;AAAA,QACA,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,YACP,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,eAAe;AAAA,YACf,KAAK;AAAA,YACL,UAAU;AAAA,cACR,gBAAgB;AAAA,YAClB;AAAA,YACA,UAAU;AAAA,cACR;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO;AAAA,gBACP,KAAK;AAAA,gBACL,SAAS;AAAA,cACX;AAAA,YACF;AAAA,UACF;AAAA,UACA,KAAK;AAAA,UACL,KAAK;AAAA,UACL;AAAA,QACF;AAAA,MACF;AAEA,YAAM,cACN;AAAA,QACE,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,QACV,UAAU;AAAA,UACR;AAAA,YACE,WAAW;AAAA,YACX,OAAO,KAAK;AAAA,YACZ,WAAW;AAAA,UACb;AAAA,QACF;AAAA,MACF;AAGA,YAAM,eAAe;AAAA,QACnB;AAAA,UACE,WAAW;AAAA,UACX,OAAO;AAAA,UACP,KAAK;AAAA,UACL,cAAc;AAAA,UACd,YAAY;AAAA,UACZ,gBAAgB;AAAA,UAChB,WAAW;AAAA,UACX,UAAU;AAAA,YACR;AAAA;AAAA,cACE,WAAW;AAAA,cACX,OAAO;AAAA,YACT;AAAA,YACA,KAAK;AAAA,YACL,KAAK;AAAA,YACL;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAEA,YAAM,eACN;AAAA,QACE,WAAW;AAAA,QACX,OAAO,KAAK;AAAA,QACZ,WAAW;AAAA,MACb;AAEA,YAAM,aAAa,SAAS,eAAe,KAAK,UAAU;AACxD,cAAM,OAAO,KAAK;AAAA,UAChB;AAAA,YACE,WAAW;AAAA,YACX;AAAA,YACA;AAAA,YACA,YAAY;AAAA,YACZ,UAAU,CAAC,EAAE,OAAO,YAAY;AAAA,UAClC;AAAA,UACA,YAAY,CAAC;AAAA,QACf;AACA,aAAK,SAAS,KAAK,YAAY;AAC/B,aAAK,SAAS,KAAK,KAAK,aAAa;AACrC,aAAK,SAAS,KAAK,KAAK,oBAAoB;AAC5C,aAAK,SAAS,KAAK,eAAe;AAClC,eAAO;AAAA,MACT;AAEA,YAAM,eACN;AAAA;AAAA,QACE,WAAW;AAAA,QACX,OAAO,SAAS,SAAS,SAAS,MAAM,GAAG,EAAE,KAAK,GAAG,IAAI;AAAA,MAC3D;AAEA,YAAM,aACN;AAAA,QACE,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU,CAAC,KAAK,gBAAgB;AAAA,QAChC,WAAW;AAAA,MACb;AAEA,YAAM,eACN;AAAA;AAAA,QAEE,OAAO,KAAK,sBAAsB;AAAA,QAClC,aAAa;AAAA,QACb,UAAU;AAAA,QACV,WAAW;AAAA,QACX,UAAU;AAAA,UACR;AAAA,YACE,eAAe,SAAS;AAAA,UAC1B;AAAA,UACA;AAAA,UACA;AAAA;AAAA,YACE,WAAW;AAAA,YACX,OAAO,KAAK;AAAA,YACZ,WAAW;AAAA,UACb;AAAA,QACF;AAAA,MACF;AAEA,YAAM,sBACN;AAAA;AAAA,QAEE,OAAO;AAAA,QACP,KAAK;AAAA,QACL,WAAW;AAAA,QACX,UAAU;AAAA,UACR,UAAU,SAAS;AAAA,UACnB,SAAS,SAAS;AAAA,QACpB;AAAA,QACA,UAAU;AAAA,UACR,KAAK;AAAA,UACL,KAAK;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAEA,mBAAa,SAAS,KAAK,mBAAmB;AAE9C,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,CAAC,KAAK;AAAA,QACf,kBAAkB;AAAA;AAAA,QAClB,UAAU;AAAA,QACV,SAAS;AAAA,QACT,UAAU;AAAA,UACR,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,UACT;AAAA,UACA,WAAW,gBAAgB,GAAG;AAAA,UAC9B,WAAW,MAAM,GAAG;AAAA,UACpB;AAAA,YACE,eAAe;AAAA,YACf,KAAK;AAAA;AAAA,YAEL,WAAW;AAAA,YACX,UAAU;AAAA,cACR,KAAK;AAAA,cACL;AAAA,cACA;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA;AAAA;AAAA,YAEE,UAAU;AAAA,cACR;AAAA,gBACE,OAAO,KAAK,sBAAsB,QAAQ,KAAK;AAAA,cACjD;AAAA,cACA;AAAA,gBACE,OAAO,KAAK,sBAAsB;AAAA,cACpC;AAAA,YACF;AAAA,YACA,WAAW;AAAA,UACb;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}