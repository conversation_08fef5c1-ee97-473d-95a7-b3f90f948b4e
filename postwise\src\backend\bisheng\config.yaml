# 数据库配置
database_url:
  "mysql+pymysql://root:gAAAAABlp4b4c59FeVGF_OQRVf6NOUIGdxq8246EBD-b0hdK_jVKRs1x4PoAn0A6C5S6IiFKmWn0Nm5eBUWu-7jxcqw6TiVjQA==@*************:3306/bisheng?charset=utf8mb4"

# 缓存配置
redis_url: "redis://*************:6379/1"

# celery的broken地址
celery_redis_url: "redis://*************:6379/2"

# 知识库的milvus和es配置
vector_stores:
  milvus:
    connection_args: '{"host":"*************","port":"19530","user":"","password":"","secure":false}'
    is_partition: 'true'
    partition_suffix: "1"
  elasticsearch:
    url: "http://*************:9200"
    ssl_verify: '{}'

# 对象存储
object_storage:
  type: minio
  minio:
    schema: 'false'
    cert_check: 'false'
    endpoint: "*************:9100"
    sharepoint: "*************:9100"
    access_key: "minioadmin"
    secret_key: "minioadmin"

environment:
  env: dev
  uns_support: [ 'png','jpg','jpeg','bmp','doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx', 'txt', 'md', 'html', 'pdf', 'csv', 'tiff' ]

# 日志配置
logger_conf:
  level: DEBUG
  format: '<level>[{time:YYYY-MM-DD HH:mm:ss.SSSSSS}] [{level.name} process-{process.id}-{thread.id} {name}:{line}]</level> - <level>trace={extra[trace_id]} {message}</level>'
  handlers:
    - sink: "./bisheng.log"
      level: INFO
      format: '<level>[{time:YYYY-MM-DD HH:mm:ss.SSSSSS}] [{level.name} process-{process.id}-{thread.id} {name}:{line}]</level> - <level>trace={extra[trace_id]} {message}</level>'
      rotation: "00:00"
      retention: "3 Days"
      enqueue: true
    - sink: "./statistic.log"
      level: INFO
      filter: "lambda record: record['level'].name == 'INFO' and record['message'].startswith('k=s')"
      format: "[{time:YYYY-MM-DD HH:mm:ss.SSSSSS}]|{level}|BISHENG|{extra[trace_id]}||{process.id}|{thread.id}|||#EX_ERR:POS={name},line {line},ERR=500,EMSG={message}"
      rotation: "00:00"
      retention: "3 Days"
      enqueue: true