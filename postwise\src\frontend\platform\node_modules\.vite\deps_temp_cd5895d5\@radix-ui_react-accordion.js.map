{"version": 3, "sources": ["../../@radix-ui/react-accordion/src/accordion.tsx", "../../@radix-ui/react-collapsible/src/collapsible.tsx"], "sourcesContent": ["import React from 'react';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { createCollection } from '@radix-ui/react-collection';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport * as CollapsiblePrimitive from '@radix-ui/react-collapsible';\nimport { createCollapsibleScope } from '@radix-ui/react-collapsible';\nimport { useId } from '@radix-ui/react-id';\n\nimport type { Scope } from '@radix-ui/react-context';\nimport { useDirection } from '@radix-ui/react-direction';\n\ntype Direction = 'ltr' | 'rtl';\n\n/* -------------------------------------------------------------------------------------------------\n * Accordion\n * -----------------------------------------------------------------------------------------------*/\n\nconst ACCORDION_NAME = 'Accordion';\nconst ACCORDION_KEYS = ['Home', 'End', 'ArrowDown', 'ArrowUp', 'ArrowLeft', 'ArrowRight'];\n\nconst [Collection, useCollection, createCollectionScope] =\n  createCollection<AccordionTriggerElement>(ACCORDION_NAME);\n\ntype ScopedProps<P> = P & { __scopeAccordion?: Scope };\nconst [createAccordionContext, createAccordionScope] = createContextScope(ACCORDION_NAME, [\n  createCollectionScope,\n  createCollapsibleScope,\n]);\nconst useCollapsibleScope = createCollapsibleScope();\n\ntype AccordionElement = AccordionImplMultipleElement | AccordionImplSingleElement;\ninterface AccordionSingleProps extends AccordionImplSingleProps {\n  type: 'single';\n}\ninterface AccordionMultipleProps extends AccordionImplMultipleProps {\n  type: 'multiple';\n}\n\nconst Accordion = React.forwardRef<AccordionElement, AccordionSingleProps | AccordionMultipleProps>(\n  (props: ScopedProps<AccordionSingleProps | AccordionMultipleProps>, forwardedRef) => {\n    const { type, ...accordionProps } = props;\n    const singleProps = accordionProps as AccordionImplSingleProps;\n    const multipleProps = accordionProps as AccordionImplMultipleProps;\n    return (\n      <Collection.Provider scope={props.__scopeAccordion}>\n        {type === 'multiple' ? (\n          <AccordionImplMultiple {...multipleProps} ref={forwardedRef} />\n        ) : (\n          <AccordionImplSingle {...singleProps} ref={forwardedRef} />\n        )}\n      </Collection.Provider>\n    );\n  }\n);\n\nAccordion.displayName = ACCORDION_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype AccordionValueContextValue = {\n  value: string[];\n  onItemOpen(value: string): void;\n  onItemClose(value: string): void;\n};\n\nconst [AccordionValueProvider, useAccordionValueContext] =\n  createAccordionContext<AccordionValueContextValue>(ACCORDION_NAME);\n\nconst [AccordionCollapsibleProvider, useAccordionCollapsibleContext] = createAccordionContext(\n  ACCORDION_NAME,\n  { collapsible: false }\n);\n\ntype AccordionImplSingleElement = AccordionImplElement;\ninterface AccordionImplSingleProps extends AccordionImplProps {\n  /**\n   * The controlled stateful value of the accordion item whose content is expanded.\n   */\n  value?: string;\n  /**\n   * The value of the item whose content is expanded when the accordion is initially rendered. Use\n   * `defaultValue` if you do not need to control the state of an accordion.\n   */\n  defaultValue?: string;\n  /**\n   * The callback that fires when the state of the accordion changes.\n   */\n  onValueChange?(value: string): void;\n  /**\n   * Whether an accordion item can be collapsed after it has been opened.\n   * @default false\n   */\n  collapsible?: boolean;\n}\n\nconst AccordionImplSingle = React.forwardRef<AccordionImplSingleElement, AccordionImplSingleProps>(\n  (props: ScopedProps<AccordionImplSingleProps>, forwardedRef) => {\n    const {\n      value: valueProp,\n      defaultValue,\n      onValueChange = () => {},\n      collapsible = false,\n      ...accordionSingleProps\n    } = props;\n\n    const [value, setValue] = useControllableState({\n      prop: valueProp,\n      defaultProp: defaultValue ?? '',\n      onChange: onValueChange,\n      caller: ACCORDION_NAME,\n    });\n\n    return (\n      <AccordionValueProvider\n        scope={props.__scopeAccordion}\n        value={React.useMemo(() => (value ? [value] : []), [value])}\n        onItemOpen={setValue}\n        onItemClose={React.useCallback(() => collapsible && setValue(''), [collapsible, setValue])}\n      >\n        <AccordionCollapsibleProvider scope={props.__scopeAccordion} collapsible={collapsible}>\n          <AccordionImpl {...accordionSingleProps} ref={forwardedRef} />\n        </AccordionCollapsibleProvider>\n      </AccordionValueProvider>\n    );\n  }\n);\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype AccordionImplMultipleElement = AccordionImplElement;\ninterface AccordionImplMultipleProps extends AccordionImplProps {\n  /**\n   * The controlled stateful value of the accordion items whose contents are expanded.\n   */\n  value?: string[];\n  /**\n   * The value of the items whose contents are expanded when the accordion is initially rendered. Use\n   * `defaultValue` if you do not need to control the state of an accordion.\n   */\n  defaultValue?: string[];\n  /**\n   * The callback that fires when the state of the accordion changes.\n   */\n  onValueChange?(value: string[]): void;\n}\n\nconst AccordionImplMultiple = React.forwardRef<\n  AccordionImplMultipleElement,\n  AccordionImplMultipleProps\n>((props: ScopedProps<AccordionImplMultipleProps>, forwardedRef) => {\n  const {\n    value: valueProp,\n    defaultValue,\n    onValueChange = () => {},\n    ...accordionMultipleProps\n  } = props;\n\n  const [value, setValue] = useControllableState({\n    prop: valueProp,\n    defaultProp: defaultValue ?? [],\n    onChange: onValueChange,\n    caller: ACCORDION_NAME,\n  });\n\n  const handleItemOpen = React.useCallback(\n    (itemValue: string) => setValue((prevValue = []) => [...prevValue, itemValue]),\n    [setValue]\n  );\n\n  const handleItemClose = React.useCallback(\n    (itemValue: string) =>\n      setValue((prevValue = []) => prevValue.filter((value) => value !== itemValue)),\n    [setValue]\n  );\n\n  return (\n    <AccordionValueProvider\n      scope={props.__scopeAccordion}\n      value={value}\n      onItemOpen={handleItemOpen}\n      onItemClose={handleItemClose}\n    >\n      <AccordionCollapsibleProvider scope={props.__scopeAccordion} collapsible={true}>\n        <AccordionImpl {...accordionMultipleProps} ref={forwardedRef} />\n      </AccordionCollapsibleProvider>\n    </AccordionValueProvider>\n  );\n});\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype AccordionImplContextValue = {\n  disabled?: boolean;\n  direction: AccordionImplProps['dir'];\n  orientation: AccordionImplProps['orientation'];\n};\n\nconst [AccordionImplProvider, useAccordionContext] =\n  createAccordionContext<AccordionImplContextValue>(ACCORDION_NAME);\n\ntype AccordionImplElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface AccordionImplProps extends PrimitiveDivProps {\n  /**\n   * Whether or not an accordion is disabled from user interaction.\n   *\n   * @defaultValue false\n   */\n  disabled?: boolean;\n  /**\n   * The layout in which the Accordion operates.\n   * @default vertical\n   */\n  orientation?: React.AriaAttributes['aria-orientation'];\n  /**\n   * The language read direction.\n   */\n  dir?: Direction;\n}\n\nconst AccordionImpl = React.forwardRef<AccordionImplElement, AccordionImplProps>(\n  (props: ScopedProps<AccordionImplProps>, forwardedRef) => {\n    const { __scopeAccordion, disabled, dir, orientation = 'vertical', ...accordionProps } = props;\n    const accordionRef = React.useRef<AccordionImplElement>(null);\n    const composedRefs = useComposedRefs(accordionRef, forwardedRef);\n    const getItems = useCollection(__scopeAccordion);\n    const direction = useDirection(dir);\n    const isDirectionLTR = direction === 'ltr';\n\n    const handleKeyDown = composeEventHandlers(props.onKeyDown, (event) => {\n      if (!ACCORDION_KEYS.includes(event.key)) return;\n      const target = event.target as HTMLElement;\n      const triggerCollection = getItems().filter((item) => !item.ref.current?.disabled);\n      const triggerIndex = triggerCollection.findIndex((item) => item.ref.current === target);\n      const triggerCount = triggerCollection.length;\n\n      if (triggerIndex === -1) return;\n\n      // Prevents page scroll while user is navigating\n      event.preventDefault();\n\n      let nextIndex = triggerIndex;\n      const homeIndex = 0;\n      const endIndex = triggerCount - 1;\n\n      const moveNext = () => {\n        nextIndex = triggerIndex + 1;\n        if (nextIndex > endIndex) {\n          nextIndex = homeIndex;\n        }\n      };\n\n      const movePrev = () => {\n        nextIndex = triggerIndex - 1;\n        if (nextIndex < homeIndex) {\n          nextIndex = endIndex;\n        }\n      };\n\n      switch (event.key) {\n        case 'Home':\n          nextIndex = homeIndex;\n          break;\n        case 'End':\n          nextIndex = endIndex;\n          break;\n        case 'ArrowRight':\n          if (orientation === 'horizontal') {\n            if (isDirectionLTR) {\n              moveNext();\n            } else {\n              movePrev();\n            }\n          }\n          break;\n        case 'ArrowDown':\n          if (orientation === 'vertical') {\n            moveNext();\n          }\n          break;\n        case 'ArrowLeft':\n          if (orientation === 'horizontal') {\n            if (isDirectionLTR) {\n              movePrev();\n            } else {\n              moveNext();\n            }\n          }\n          break;\n        case 'ArrowUp':\n          if (orientation === 'vertical') {\n            movePrev();\n          }\n          break;\n      }\n\n      const clampedIndex = nextIndex % triggerCount;\n      triggerCollection[clampedIndex]!.ref.current?.focus();\n    });\n\n    return (\n      <AccordionImplProvider\n        scope={__scopeAccordion}\n        disabled={disabled}\n        direction={dir}\n        orientation={orientation}\n      >\n        <Collection.Slot scope={__scopeAccordion}>\n          <Primitive.div\n            {...accordionProps}\n            data-orientation={orientation}\n            ref={composedRefs}\n            onKeyDown={disabled ? undefined : handleKeyDown}\n          />\n        </Collection.Slot>\n      </AccordionImplProvider>\n    );\n  }\n);\n\n/* -------------------------------------------------------------------------------------------------\n * AccordionItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_NAME = 'AccordionItem';\n\ntype AccordionItemContextValue = { open?: boolean; disabled?: boolean; triggerId: string };\nconst [AccordionItemProvider, useAccordionItemContext] =\n  createAccordionContext<AccordionItemContextValue>(ITEM_NAME);\n\ntype AccordionItemElement = React.ComponentRef<typeof CollapsiblePrimitive.Root>;\ntype CollapsibleProps = React.ComponentPropsWithoutRef<typeof CollapsiblePrimitive.Root>;\ninterface AccordionItemProps\n  extends Omit<CollapsibleProps, 'open' | 'defaultOpen' | 'onOpenChange'> {\n  /**\n   * Whether or not an accordion item is disabled from user interaction.\n   *\n   * @defaultValue false\n   */\n  disabled?: boolean;\n  /**\n   * A string value for the accordion item. All items within an accordion should use a unique value.\n   */\n  value: string;\n}\n\n/**\n * `AccordionItem` contains all of the parts of a collapsible section inside of an `Accordion`.\n */\nconst AccordionItem = React.forwardRef<AccordionItemElement, AccordionItemProps>(\n  (props: ScopedProps<AccordionItemProps>, forwardedRef) => {\n    const { __scopeAccordion, value, ...accordionItemProps } = props;\n    const accordionContext = useAccordionContext(ITEM_NAME, __scopeAccordion);\n    const valueContext = useAccordionValueContext(ITEM_NAME, __scopeAccordion);\n    const collapsibleScope = useCollapsibleScope(__scopeAccordion);\n    const triggerId = useId();\n    const open = (value && valueContext.value.includes(value)) || false;\n    const disabled = accordionContext.disabled || props.disabled;\n\n    return (\n      <AccordionItemProvider\n        scope={__scopeAccordion}\n        open={open}\n        disabled={disabled}\n        triggerId={triggerId}\n      >\n        <CollapsiblePrimitive.Root\n          data-orientation={accordionContext.orientation}\n          data-state={getState(open)}\n          {...collapsibleScope}\n          {...accordionItemProps}\n          ref={forwardedRef}\n          disabled={disabled}\n          open={open}\n          onOpenChange={(open) => {\n            if (open) {\n              valueContext.onItemOpen(value);\n            } else {\n              valueContext.onItemClose(value);\n            }\n          }}\n        />\n      </AccordionItemProvider>\n    );\n  }\n);\n\nAccordionItem.displayName = ITEM_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AccordionHeader\n * -----------------------------------------------------------------------------------------------*/\n\nconst HEADER_NAME = 'AccordionHeader';\n\ntype AccordionHeaderElement = React.ComponentRef<typeof Primitive.h3>;\ntype PrimitiveHeading3Props = React.ComponentPropsWithoutRef<typeof Primitive.h3>;\ninterface AccordionHeaderProps extends PrimitiveHeading3Props {}\n\n/**\n * `AccordionHeader` contains the content for the parts of an `AccordionItem` that will be visible\n * whether or not its content is collapsed.\n */\nconst AccordionHeader = React.forwardRef<AccordionHeaderElement, AccordionHeaderProps>(\n  (props: ScopedProps<AccordionHeaderProps>, forwardedRef) => {\n    const { __scopeAccordion, ...headerProps } = props;\n    const accordionContext = useAccordionContext(ACCORDION_NAME, __scopeAccordion);\n    const itemContext = useAccordionItemContext(HEADER_NAME, __scopeAccordion);\n    return (\n      <Primitive.h3\n        data-orientation={accordionContext.orientation}\n        data-state={getState(itemContext.open)}\n        data-disabled={itemContext.disabled ? '' : undefined}\n        {...headerProps}\n        ref={forwardedRef}\n      />\n    );\n  }\n);\n\nAccordionHeader.displayName = HEADER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AccordionTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'AccordionTrigger';\n\ntype AccordionTriggerElement = React.ComponentRef<typeof CollapsiblePrimitive.Trigger>;\ntype CollapsibleTriggerProps = React.ComponentPropsWithoutRef<typeof CollapsiblePrimitive.Trigger>;\ninterface AccordionTriggerProps extends CollapsibleTriggerProps {}\n\n/**\n * `AccordionTrigger` is the trigger that toggles the collapsed state of an `AccordionItem`. It\n * should always be nested inside of an `AccordionHeader`.\n */\nconst AccordionTrigger = React.forwardRef<AccordionTriggerElement, AccordionTriggerProps>(\n  (props: ScopedProps<AccordionTriggerProps>, forwardedRef) => {\n    const { __scopeAccordion, ...triggerProps } = props;\n    const accordionContext = useAccordionContext(ACCORDION_NAME, __scopeAccordion);\n    const itemContext = useAccordionItemContext(TRIGGER_NAME, __scopeAccordion);\n    const collapsibleContext = useAccordionCollapsibleContext(TRIGGER_NAME, __scopeAccordion);\n    const collapsibleScope = useCollapsibleScope(__scopeAccordion);\n    return (\n      <Collection.ItemSlot scope={__scopeAccordion}>\n        <CollapsiblePrimitive.Trigger\n          aria-disabled={(itemContext.open && !collapsibleContext.collapsible) || undefined}\n          data-orientation={accordionContext.orientation}\n          id={itemContext.triggerId}\n          {...collapsibleScope}\n          {...triggerProps}\n          ref={forwardedRef}\n        />\n      </Collection.ItemSlot>\n    );\n  }\n);\n\nAccordionTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AccordionContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'AccordionContent';\n\ntype AccordionContentElement = React.ComponentRef<typeof CollapsiblePrimitive.Content>;\ntype CollapsibleContentProps = React.ComponentPropsWithoutRef<typeof CollapsiblePrimitive.Content>;\ninterface AccordionContentProps extends CollapsibleContentProps {}\n\n/**\n * `AccordionContent` contains the collapsible content for an `AccordionItem`.\n */\nconst AccordionContent = React.forwardRef<AccordionContentElement, AccordionContentProps>(\n  (props: ScopedProps<AccordionContentProps>, forwardedRef) => {\n    const { __scopeAccordion, ...contentProps } = props;\n    const accordionContext = useAccordionContext(ACCORDION_NAME, __scopeAccordion);\n    const itemContext = useAccordionItemContext(CONTENT_NAME, __scopeAccordion);\n    const collapsibleScope = useCollapsibleScope(__scopeAccordion);\n    return (\n      <CollapsiblePrimitive.Content\n        role=\"region\"\n        aria-labelledby={itemContext.triggerId}\n        data-orientation={accordionContext.orientation}\n        {...collapsibleScope}\n        {...contentProps}\n        ref={forwardedRef}\n        style={{\n          ['--radix-accordion-content-height' as any]: 'var(--radix-collapsible-content-height)',\n          ['--radix-accordion-content-width' as any]: 'var(--radix-collapsible-content-width)',\n          ...props.style,\n        }}\n      />\n    );\n  }\n);\n\nAccordionContent.displayName = CONTENT_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction getState(open?: boolean) {\n  return open ? 'open' : 'closed';\n}\n\nconst Root = Accordion;\nconst Item = AccordionItem;\nconst Header = AccordionHeader;\nconst Trigger = AccordionTrigger;\nconst Content = AccordionContent;\n\nexport {\n  createAccordionScope,\n  //\n  Accordion,\n  AccordionItem,\n  AccordionHeader,\n  AccordionTrigger,\n  AccordionContent,\n  //\n  Root,\n  Item,\n  Header,\n  Trigger,\n  Content,\n};\nexport type {\n  AccordionSingleProps,\n  AccordionMultipleProps,\n  AccordionItemProps,\n  AccordionHeaderProps,\n  AccordionTriggerProps,\n  AccordionContentProps,\n};\n", "import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { Presence } from '@radix-ui/react-presence';\nimport { useId } from '@radix-ui/react-id';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * Collapsible\n * -----------------------------------------------------------------------------------------------*/\n\nconst COLLAPSIBLE_NAME = 'Collapsible';\n\ntype ScopedProps<P> = P & { __scopeCollapsible?: Scope };\nconst [createCollapsibleContext, createCollapsibleScope] = createContextScope(COLLAPSIBLE_NAME);\n\ntype CollapsibleContextValue = {\n  contentId: string;\n  disabled?: boolean;\n  open: boolean;\n  onOpenToggle(): void;\n};\n\nconst [CollapsibleProvider, useCollapsibleContext] =\n  createCollapsibleContext<CollapsibleContextValue>(COLLAPSIBLE_NAME);\n\ntype CollapsibleElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface CollapsibleProps extends PrimitiveDivProps {\n  defaultOpen?: boolean;\n  open?: boolean;\n  disabled?: boolean;\n  onOpenChange?(open: boolean): void;\n}\n\nconst Collapsible = React.forwardRef<CollapsibleElement, CollapsibleProps>(\n  (props: ScopedProps<CollapsibleProps>, forwardedRef) => {\n    const {\n      __scopeCollapsible,\n      open: openProp,\n      defaultOpen,\n      disabled,\n      onOpenChange,\n      ...collapsibleProps\n    } = props;\n\n    const [open, setOpen] = useControllableState({\n      prop: openProp,\n      defaultProp: defaultOpen ?? false,\n      onChange: onOpenChange,\n      caller: COLLAPSIBLE_NAME,\n    });\n\n    return (\n      <CollapsibleProvider\n        scope={__scopeCollapsible}\n        disabled={disabled}\n        contentId={useId()}\n        open={open}\n        onOpenToggle={React.useCallback(() => setOpen((prevOpen) => !prevOpen), [setOpen])}\n      >\n        <Primitive.div\n          data-state={getState(open)}\n          data-disabled={disabled ? '' : undefined}\n          {...collapsibleProps}\n          ref={forwardedRef}\n        />\n      </CollapsibleProvider>\n    );\n  }\n);\n\nCollapsible.displayName = COLLAPSIBLE_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * CollapsibleTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'CollapsibleTrigger';\n\ntype CollapsibleTriggerElement = React.ComponentRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface CollapsibleTriggerProps extends PrimitiveButtonProps {}\n\nconst CollapsibleTrigger = React.forwardRef<CollapsibleTriggerElement, CollapsibleTriggerProps>(\n  (props: ScopedProps<CollapsibleTriggerProps>, forwardedRef) => {\n    const { __scopeCollapsible, ...triggerProps } = props;\n    const context = useCollapsibleContext(TRIGGER_NAME, __scopeCollapsible);\n    return (\n      <Primitive.button\n        type=\"button\"\n        aria-controls={context.contentId}\n        aria-expanded={context.open || false}\n        data-state={getState(context.open)}\n        data-disabled={context.disabled ? '' : undefined}\n        disabled={context.disabled}\n        {...triggerProps}\n        ref={forwardedRef}\n        onClick={composeEventHandlers(props.onClick, context.onOpenToggle)}\n      />\n    );\n  }\n);\n\nCollapsibleTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * CollapsibleContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'CollapsibleContent';\n\ntype CollapsibleContentElement = CollapsibleContentImplElement;\ninterface CollapsibleContentProps extends Omit<CollapsibleContentImplProps, 'present'> {\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst CollapsibleContent = React.forwardRef<CollapsibleContentElement, CollapsibleContentProps>(\n  (props: ScopedProps<CollapsibleContentProps>, forwardedRef) => {\n    const { forceMount, ...contentProps } = props;\n    const context = useCollapsibleContext(CONTENT_NAME, props.__scopeCollapsible);\n    return (\n      <Presence present={forceMount || context.open}>\n        {({ present }) => (\n          <CollapsibleContentImpl {...contentProps} ref={forwardedRef} present={present} />\n        )}\n      </Presence>\n    );\n  }\n);\n\nCollapsibleContent.displayName = CONTENT_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype CollapsibleContentImplElement = React.ComponentRef<typeof Primitive.div>;\ninterface CollapsibleContentImplProps extends PrimitiveDivProps {\n  present: boolean;\n}\n\nconst CollapsibleContentImpl = React.forwardRef<\n  CollapsibleContentImplElement,\n  CollapsibleContentImplProps\n>((props: ScopedProps<CollapsibleContentImplProps>, forwardedRef) => {\n  const { __scopeCollapsible, present, children, ...contentProps } = props;\n  const context = useCollapsibleContext(CONTENT_NAME, __scopeCollapsible);\n  const [isPresent, setIsPresent] = React.useState(present);\n  const ref = React.useRef<CollapsibleContentImplElement>(null);\n  const composedRefs = useComposedRefs(forwardedRef, ref);\n  const heightRef = React.useRef<number | undefined>(0);\n  const height = heightRef.current;\n  const widthRef = React.useRef<number | undefined>(0);\n  const width = widthRef.current;\n  // when opening we want it to immediately open to retrieve dimensions\n  // when closing we delay `present` to retrieve dimensions before closing\n  const isOpen = context.open || isPresent;\n  const isMountAnimationPreventedRef = React.useRef(isOpen);\n  const originalStylesRef = React.useRef<Record<string, string>>(undefined);\n\n  React.useEffect(() => {\n    const rAF = requestAnimationFrame(() => (isMountAnimationPreventedRef.current = false));\n    return () => cancelAnimationFrame(rAF);\n  }, []);\n\n  useLayoutEffect(() => {\n    const node = ref.current;\n    if (node) {\n      originalStylesRef.current = originalStylesRef.current || {\n        transitionDuration: node.style.transitionDuration,\n        animationName: node.style.animationName,\n      };\n      // block any animations/transitions so the element renders at its full dimensions\n      node.style.transitionDuration = '0s';\n      node.style.animationName = 'none';\n\n      // get width and height from full dimensions\n      const rect = node.getBoundingClientRect();\n      heightRef.current = rect.height;\n      widthRef.current = rect.width;\n\n      // kick off any animations/transitions that were originally set up if it isn't the initial mount\n      if (!isMountAnimationPreventedRef.current) {\n        node.style.transitionDuration = originalStylesRef.current.transitionDuration!;\n        node.style.animationName = originalStylesRef.current.animationName!;\n      }\n\n      setIsPresent(present);\n    }\n    /**\n     * depends on `context.open` because it will change to `false`\n     * when a close is triggered but `present` will be `false` on\n     * animation end (so when close finishes). This allows us to\n     * retrieve the dimensions *before* closing.\n     */\n  }, [context.open, present]);\n\n  return (\n    <Primitive.div\n      data-state={getState(context.open)}\n      data-disabled={context.disabled ? '' : undefined}\n      id={context.contentId}\n      hidden={!isOpen}\n      {...contentProps}\n      ref={composedRefs}\n      style={{\n        [`--radix-collapsible-content-height` as any]: height ? `${height}px` : undefined,\n        [`--radix-collapsible-content-width` as any]: width ? `${width}px` : undefined,\n        ...props.style,\n      }}\n    >\n      {isOpen && children}\n    </Primitive.div>\n  );\n});\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction getState(open?: boolean) {\n  return open ? 'open' : 'closed';\n}\n\nconst Root = Collapsible;\nconst Trigger = CollapsibleTrigger;\nconst Content = CollapsibleContent;\n\nexport {\n  createCollapsibleScope,\n  //\n  Collapsible,\n  CollapsibleTrigger,\n  CollapsibleContent,\n  //\n  Root,\n  Trigger,\n  Content,\n};\nexport type { CollapsibleProps, CollapsibleTriggerProps, CollapsibleContentProps };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,mBAAkB;;;ACAlB,YAAuB;AAkEf,yBAAA;AAlDR,IAAM,mBAAmB;AAGzB,IAAM,CAAC,0BAA0B,sBAAsB,IAAI,mBAAmB,gBAAgB;AAS9F,IAAM,CAAC,qBAAqB,qBAAqB,IAC/C,yBAAkD,gBAAgB;AAWpE,IAAM,cAAoB;EACxB,CAAC,OAAsC,iBAAiB;AACtD,UAAM;MACJ;MACA,MAAM;MACN;MACA;MACA;MACA,GAAG;IACL,IAAI;AAEJ,UAAM,CAAC,MAAM,OAAO,IAAI,qBAAqB;MAC3C,MAAM;MACN,aAAa,eAAe;MAC5B,UAAU;MACV,QAAQ;IACV,CAAC;AAED,eACE;MAAC;MAAA;QACC,OAAO;QACP;QACA,WAAW,MAAM;QACjB;QACA,cAAoB,kBAAY,MAAM,QAAQ,CAAC,aAAa,CAAC,QAAQ,GAAG,CAAC,OAAO,CAAC;QAEjF,cAAA;UAAC,UAAU;UAAV;YACC,cAAY,SAAS,IAAI;YACzB,iBAAe,WAAW,KAAK;YAC9B,GAAG;YACJ,KAAK;UAAA;QACP;MAAA;IACF;EAEJ;AACF;AAEA,YAAY,cAAc;AAM1B,IAAM,eAAe;AAMrB,IAAM,qBAA2B;EAC/B,CAAC,OAA6C,iBAAiB;AAC7D,UAAM,EAAE,oBAAoB,GAAG,aAAa,IAAI;AAChD,UAAM,UAAU,sBAAsB,cAAc,kBAAkB;AACtE,eACE;MAAC,UAAU;MAAV;QACC,MAAK;QACL,iBAAe,QAAQ;QACvB,iBAAe,QAAQ,QAAQ;QAC/B,cAAY,SAAS,QAAQ,IAAI;QACjC,iBAAe,QAAQ,WAAW,KAAK;QACvC,UAAU,QAAQ;QACjB,GAAG;QACJ,KAAK;QACL,SAAS,qBAAqB,MAAM,SAAS,QAAQ,YAAY;MAAA;IACnE;EAEJ;AACF;AAEA,mBAAmB,cAAc;AAMjC,IAAM,eAAe;AAWrB,IAAM,qBAA2B;EAC/B,CAAC,OAA6C,iBAAiB;AAC7D,UAAM,EAAE,YAAY,GAAG,aAAa,IAAI;AACxC,UAAM,UAAU,sBAAsB,cAAc,MAAM,kBAAkB;AAC5E,eACE,wBAAC,UAAA,EAAS,SAAS,cAAc,QAAQ,MACtC,UAAA,CAAC,EAAE,QAAQ,UACV,wBAAC,wBAAA,EAAwB,GAAG,cAAc,KAAK,cAAc,QAAA,CAAkB,EAAA,CAEnF;EAEJ;AACF;AAEA,mBAAmB,cAAc;AASjC,IAAM,yBAA+B,iBAGnC,CAAC,OAAiD,iBAAiB;AACnE,QAAM,EAAE,oBAAoB,SAAS,UAAU,GAAG,aAAa,IAAI;AACnE,QAAM,UAAU,sBAAsB,cAAc,kBAAkB;AACtE,QAAM,CAAC,WAAW,YAAY,IAAU,eAAS,OAAO;AACxD,QAAM,MAAY,aAAsC,IAAI;AAC5D,QAAM,eAAe,gBAAgB,cAAc,GAAG;AACtD,QAAM,YAAkB,aAA2B,CAAC;AACpD,QAAM,SAAS,UAAU;AACzB,QAAM,WAAiB,aAA2B,CAAC;AACnD,QAAM,QAAQ,SAAS;AAGvB,QAAM,SAAS,QAAQ,QAAQ;AAC/B,QAAM,+BAAqC,aAAO,MAAM;AACxD,QAAM,oBAA0B,aAA+B,MAAS;AAElE,EAAA,gBAAU,MAAM;AACpB,UAAM,MAAM,sBAAsB,MAAO,6BAA6B,UAAU,KAAM;AACtF,WAAO,MAAM,qBAAqB,GAAG;EACvC,GAAG,CAAC,CAAC;AAEL,mBAAgB,MAAM;AACpB,UAAM,OAAO,IAAI;AACjB,QAAI,MAAM;AACR,wBAAkB,UAAU,kBAAkB,WAAW;QACvD,oBAAoB,KAAK,MAAM;QAC/B,eAAe,KAAK,MAAM;MAC5B;AAEA,WAAK,MAAM,qBAAqB;AAChC,WAAK,MAAM,gBAAgB;AAG3B,YAAM,OAAO,KAAK,sBAAsB;AACxC,gBAAU,UAAU,KAAK;AACzB,eAAS,UAAU,KAAK;AAGxB,UAAI,CAAC,6BAA6B,SAAS;AACzC,aAAK,MAAM,qBAAqB,kBAAkB,QAAQ;AAC1D,aAAK,MAAM,gBAAgB,kBAAkB,QAAQ;MACvD;AAEA,mBAAa,OAAO;IACtB;EAOF,GAAG,CAAC,QAAQ,MAAM,OAAO,CAAC;AAE1B,aACE;IAAC,UAAU;IAAV;MACC,cAAY,SAAS,QAAQ,IAAI;MACjC,iBAAe,QAAQ,WAAW,KAAK;MACvC,IAAI,QAAQ;MACZ,QAAQ,CAAC;MACR,GAAG;MACJ,KAAK;MACL,OAAO;QACL,CAAC,oCAA2C,GAAG,SAAS,GAAG,MAAM,OAAO;QACxE,CAAC,mCAA0C,GAAG,QAAQ,GAAG,KAAK,OAAO;QACrE,GAAG,MAAM;MACX;MAEC,UAAA,UAAU;IAAA;EACb;AAEJ,CAAC;AAID,SAAS,SAAS,MAAgB;AAChC,SAAO,OAAO,SAAS;AACzB;AAEA,IAAM,OAAO;AACb,IAAM,UAAU;AAChB,IAAM,UAAU;;;ADvLN,IAAAA,sBAAA;AA7BV,IAAM,iBAAiB;AACvB,IAAM,iBAAiB,CAAC,QAAQ,OAAO,aAAa,WAAW,aAAa,YAAY;AAExF,IAAM,CAAC,YAAY,eAAe,qBAAqB,IACrD,iBAA0C,cAAc;AAG1D,IAAM,CAAC,wBAAwB,oBAAoB,IAAI,mBAAmB,gBAAgB;EACxF;EACA;AACF,CAAC;AACD,IAAM,sBAAsB,uBAAuB;AAUnD,IAAM,YAAY,aAAAC,QAAM;EACtB,CAAC,OAAmE,iBAAiB;AACnF,UAAM,EAAE,MAAM,GAAG,eAAe,IAAI;AACpC,UAAM,cAAc;AACpB,UAAM,gBAAgB;AACtB,eACE,yBAAC,WAAW,UAAX,EAAoB,OAAO,MAAM,kBAC/B,UAAA,SAAS,iBACR,yBAAC,uBAAA,EAAuB,GAAG,eAAe,KAAK,aAAA,CAAc,QAE7D,yBAAC,qBAAA,EAAqB,GAAG,aAAa,KAAK,aAAA,CAAc,EAAA,CAE7D;EAEJ;AACF;AAEA,UAAU,cAAc;AAUxB,IAAM,CAAC,wBAAwB,wBAAwB,IACrD,uBAAmD,cAAc;AAEnE,IAAM,CAAC,8BAA8B,8BAA8B,IAAI;EACrE;EACA,EAAE,aAAa,MAAM;AACvB;AAwBA,IAAM,sBAAsB,aAAAA,QAAM;EAChC,CAAC,OAA8C,iBAAiB;AAC9D,UAAM;MACJ,OAAO;MACP;MACA,gBAAgB,MAAM;MAAC;MACvB,cAAc;MACd,GAAG;IACL,IAAI;AAEJ,UAAM,CAAC,OAAO,QAAQ,IAAI,qBAAqB;MAC7C,MAAM;MACN,aAAa,gBAAgB;MAC7B,UAAU;MACV,QAAQ;IACV,CAAC;AAED,eACE;MAAC;MAAA;QACC,OAAO,MAAM;QACb,OAAO,aAAAA,QAAM,QAAQ,MAAO,QAAQ,CAAC,KAAK,IAAI,CAAC,GAAI,CAAC,KAAK,CAAC;QAC1D,YAAY;QACZ,aAAa,aAAAA,QAAM,YAAY,MAAM,eAAe,SAAS,EAAE,GAAG,CAAC,aAAa,QAAQ,CAAC;QAEzF,cAAA,yBAAC,8BAAA,EAA6B,OAAO,MAAM,kBAAkB,aAC3D,cAAA,yBAAC,eAAA,EAAe,GAAG,sBAAsB,KAAK,aAAA,CAAc,EAAA,CAC9D;MAAA;IACF;EAEJ;AACF;AAqBA,IAAM,wBAAwB,aAAAA,QAAM,WAGlC,CAAC,OAAgD,iBAAiB;AAClE,QAAM;IACJ,OAAO;IACP;IACA,gBAAgB,MAAM;IAAC;IACvB,GAAG;EACL,IAAI;AAEJ,QAAM,CAAC,OAAO,QAAQ,IAAI,qBAAqB;IAC7C,MAAM;IACN,aAAa,gBAAgB,CAAC;IAC9B,UAAU;IACV,QAAQ;EACV,CAAC;AAED,QAAM,iBAAiB,aAAAA,QAAM;IAC3B,CAAC,cAAsB,SAAS,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,WAAW,SAAS,CAAC;IAC7E,CAAC,QAAQ;EACX;AAEA,QAAM,kBAAkB,aAAAA,QAAM;IAC5B,CAAC,cACC,SAAS,CAAC,YAAY,CAAC,MAAM,UAAU,OAAO,CAACC,WAAUA,WAAU,SAAS,CAAC;IAC/E,CAAC,QAAQ;EACX;AAEA,aACE;IAAC;IAAA;MACC,OAAO,MAAM;MACb;MACA,YAAY;MACZ,aAAa;MAEb,cAAA,yBAAC,8BAAA,EAA6B,OAAO,MAAM,kBAAkB,aAAa,MACxE,cAAA,yBAAC,eAAA,EAAe,GAAG,wBAAwB,KAAK,aAAA,CAAc,EAAA,CAChE;IAAA;EACF;AAEJ,CAAC;AAUD,IAAM,CAAC,uBAAuB,mBAAmB,IAC/C,uBAAkD,cAAc;AAsBlE,IAAM,gBAAgB,aAAAD,QAAM;EAC1B,CAAC,OAAwC,iBAAiB;AACxD,UAAM,EAAE,kBAAkB,UAAU,KAAK,cAAc,YAAY,GAAG,eAAe,IAAI;AACzF,UAAM,eAAe,aAAAA,QAAM,OAA6B,IAAI;AAC5D,UAAM,eAAe,gBAAgB,cAAc,YAAY;AAC/D,UAAM,WAAW,cAAc,gBAAgB;AAC/C,UAAM,YAAY,aAAa,GAAG;AAClC,UAAM,iBAAiB,cAAc;AAErC,UAAM,gBAAgB,qBAAqB,MAAM,WAAW,CAAC,UAAU;;AACrE,UAAI,CAAC,eAAe,SAAS,MAAM,GAAG,EAAG;AACzC,YAAM,SAAS,MAAM;AACrB,YAAM,oBAAoB,SAAS,EAAE,OAAO,CAAC,SAAA;;AAAS,kBAACE,MAAA,KAAK,IAAI,YAAT,gBAAAA,IAAkB;OAAQ;AACjF,YAAM,eAAe,kBAAkB,UAAU,CAAC,SAAS,KAAK,IAAI,YAAY,MAAM;AACtF,YAAM,eAAe,kBAAkB;AAEvC,UAAI,iBAAiB,GAAI;AAGzB,YAAM,eAAe;AAErB,UAAI,YAAY;AAChB,YAAM,YAAY;AAClB,YAAM,WAAW,eAAe;AAEhC,YAAM,WAAW,MAAM;AACrB,oBAAY,eAAe;AAC3B,YAAI,YAAY,UAAU;AACxB,sBAAY;QACd;MACF;AAEA,YAAM,WAAW,MAAM;AACrB,oBAAY,eAAe;AAC3B,YAAI,YAAY,WAAW;AACzB,sBAAY;QACd;MACF;AAEA,cAAQ,MAAM,KAAK;QACjB,KAAK;AACH,sBAAY;AACZ;QACF,KAAK;AACH,sBAAY;AACZ;QACF,KAAK;AACH,cAAI,gBAAgB,cAAc;AAChC,gBAAI,gBAAgB;AAClB,uBAAS;YACX,OAAO;AACL,uBAAS;YACX;UACF;AACA;QACF,KAAK;AACH,cAAI,gBAAgB,YAAY;AAC9B,qBAAS;UACX;AACA;QACF,KAAK;AACH,cAAI,gBAAgB,cAAc;AAChC,gBAAI,gBAAgB;AAClB,uBAAS;YACX,OAAO;AACL,uBAAS;YACX;UACF;AACA;QACF,KAAK;AACH,cAAI,gBAAgB,YAAY;AAC9B,qBAAS;UACX;AACA;MACJ;AAEA,YAAM,eAAe,YAAY;AACjC,8BAAkB,YAAY,EAAG,IAAI,YAArC,mBAA8C;IAChD,CAAC;AAED,eACE;MAAC;MAAA;QACC,OAAO;QACP;QACA,WAAW;QACX;QAEA,cAAA,yBAAC,WAAW,MAAX,EAAgB,OAAO,kBACtB,cAAA;UAAC,UAAU;UAAV;YACE,GAAG;YACJ,oBAAkB;YAClB,KAAK;YACL,WAAW,WAAW,SAAY;UAAA;QACpC,EAAA,CACF;MAAA;IACF;EAEJ;AACF;AAMA,IAAM,YAAY;AAGlB,IAAM,CAAC,uBAAuB,uBAAuB,IACnD,uBAAkD,SAAS;AAqB7D,IAAM,gBAAgB,aAAAF,QAAM;EAC1B,CAAC,OAAwC,iBAAiB;AACxD,UAAM,EAAE,kBAAkB,OAAO,GAAG,mBAAmB,IAAI;AAC3D,UAAM,mBAAmB,oBAAoB,WAAW,gBAAgB;AACxE,UAAM,eAAe,yBAAyB,WAAW,gBAAgB;AACzE,UAAM,mBAAmB,oBAAoB,gBAAgB;AAC7D,UAAM,YAAY,MAAM;AACxB,UAAM,OAAQ,SAAS,aAAa,MAAM,SAAS,KAAK,KAAM;AAC9D,UAAM,WAAW,iBAAiB,YAAY,MAAM;AAEpD,eACE;MAAC;MAAA;QACC,OAAO;QACP;QACA;QACA;QAEA,cAAA;UAAsB;UAArB;YACC,oBAAkB,iBAAiB;YACnC,cAAYG,UAAS,IAAI;YACxB,GAAG;YACH,GAAG;YACJ,KAAK;YACL;YACA;YACA,cAAc,CAACC,UAAS;AACtB,kBAAIA,OAAM;AACR,6BAAa,WAAW,KAAK;cAC/B,OAAO;AACL,6BAAa,YAAY,KAAK;cAChC;YACF;UAAA;QACF;MAAA;IACF;EAEJ;AACF;AAEA,cAAc,cAAc;AAM5B,IAAM,cAAc;AAUpB,IAAM,kBAAkB,aAAAJ,QAAM;EAC5B,CAAC,OAA0C,iBAAiB;AAC1D,UAAM,EAAE,kBAAkB,GAAG,YAAY,IAAI;AAC7C,UAAM,mBAAmB,oBAAoB,gBAAgB,gBAAgB;AAC7E,UAAM,cAAc,wBAAwB,aAAa,gBAAgB;AACzE,eACE;MAAC,UAAU;MAAV;QACC,oBAAkB,iBAAiB;QACnC,cAAYG,UAAS,YAAY,IAAI;QACrC,iBAAe,YAAY,WAAW,KAAK;QAC1C,GAAG;QACJ,KAAK;MAAA;IACP;EAEJ;AACF;AAEA,gBAAgB,cAAc;AAM9B,IAAME,gBAAe;AAUrB,IAAM,mBAAmB,aAAAL,QAAM;EAC7B,CAAC,OAA2C,iBAAiB;AAC3D,UAAM,EAAE,kBAAkB,GAAG,aAAa,IAAI;AAC9C,UAAM,mBAAmB,oBAAoB,gBAAgB,gBAAgB;AAC7E,UAAM,cAAc,wBAAwBK,eAAc,gBAAgB;AAC1E,UAAM,qBAAqB,+BAA+BA,eAAc,gBAAgB;AACxF,UAAM,mBAAmB,oBAAoB,gBAAgB;AAC7D,eACE,yBAAC,WAAW,UAAX,EAAoB,OAAO,kBAC1B,cAAA;MAAsB;MAArB;QACC,iBAAgB,YAAY,QAAQ,CAAC,mBAAmB,eAAgB;QACxE,oBAAkB,iBAAiB;QACnC,IAAI,YAAY;QACf,GAAG;QACH,GAAG;QACJ,KAAK;MAAA;IACP,EAAA,CACF;EAEJ;AACF;AAEA,iBAAiB,cAAcA;AAM/B,IAAMC,gBAAe;AASrB,IAAM,mBAAmB,aAAAN,QAAM;EAC7B,CAAC,OAA2C,iBAAiB;AAC3D,UAAM,EAAE,kBAAkB,GAAG,aAAa,IAAI;AAC9C,UAAM,mBAAmB,oBAAoB,gBAAgB,gBAAgB;AAC7E,UAAM,cAAc,wBAAwBM,eAAc,gBAAgB;AAC1E,UAAM,mBAAmB,oBAAoB,gBAAgB;AAC7D,eACE;MAAsB;MAArB;QACC,MAAK;QACL,mBAAiB,YAAY;QAC7B,oBAAkB,iBAAiB;QAClC,GAAG;QACH,GAAG;QACJ,KAAK;QACL,OAAO;UACL,CAAC,kCAAyC,GAAG;UAC7C,CAAC,iCAAwC,GAAG;UAC5C,GAAG,MAAM;QACX;MAAA;IACF;EAEJ;AACF;AAEA,iBAAiB,cAAcA;AAI/B,SAASH,UAAS,MAAgB;AAChC,SAAO,OAAO,SAAS;AACzB;AAEA,IAAMI,QAAO;AACb,IAAM,OAAO;AACb,IAAM,SAAS;AACf,IAAMC,WAAU;AAChB,IAAMC,WAAU;", "names": ["import_jsx_runtime", "React", "value", "_a", "getState", "open", "TRIGGER_NAME", "CONTENT_NAME", "Root", "<PERSON><PERSON>", "Content"]}