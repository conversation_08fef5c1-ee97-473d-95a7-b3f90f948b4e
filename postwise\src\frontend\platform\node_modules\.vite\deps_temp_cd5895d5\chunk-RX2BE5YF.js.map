{"version": 3, "sources": ["../../highlight.js/lib/languages/elm.js"], "sourcesContent": ["/*\nLanguage: Elm\nAuthor: <PERSON><PERSON> <<EMAIL>>\nWebsite: https://elm-lang.org\nCategory: functional\n*/\n\n/** @type LanguageFn */\nfunction elm(hljs) {\n  const COMMENT = {\n    variants: [\n      hljs.COMMENT('--', '$'),\n      hljs.COMMENT(\n        /\\{-/,\n        /-\\}/,\n        {\n          contains: ['self']\n        }\n      )\n    ]\n  };\n\n  const CONSTRUCTOR = {\n    className: 'type',\n    begin: '\\\\b[A-Z][\\\\w\\']*', // TODO: other constructors (built-in, infix).\n    relevance: 0\n  };\n\n  const LIST = {\n    begin: '\\\\(',\n    end: '\\\\)',\n    illegal: '\"',\n    contains: [\n      {\n        className: 'type',\n        begin: '\\\\b[A-Z][\\\\w]*(\\\\((\\\\.\\\\.|,|\\\\w+)\\\\))?'\n      },\n      COMMENT\n    ]\n  };\n\n  const RECORD = {\n    begin: /\\{/,\n    end: /\\}/,\n    contains: LIST.contains\n  };\n\n  const CHARACTER = {\n    className: 'string',\n    begin: '\\'\\\\\\\\?.',\n    end: '\\'',\n    illegal: '.'\n  };\n\n  return {\n    name: 'Elm',\n    keywords:\n      'let in if then else case of where module import exposing ' +\n      'type alias as infix infixl infixr port effect command subscription',\n    contains: [\n\n      // Top-level constructions.\n\n      {\n        beginKeywords: 'port effect module',\n        end: 'exposing',\n        keywords: 'port effect module where command subscription exposing',\n        contains: [\n          LIST,\n          COMMENT\n        ],\n        illegal: '\\\\W\\\\.|;'\n      },\n      {\n        begin: 'import',\n        end: '$',\n        keywords: 'import as exposing',\n        contains: [\n          LIST,\n          COMMENT\n        ],\n        illegal: '\\\\W\\\\.|;'\n      },\n      {\n        begin: 'type',\n        end: '$',\n        keywords: 'type alias',\n        contains: [\n          CONSTRUCTOR,\n          LIST,\n          RECORD,\n          COMMENT\n        ]\n      },\n      {\n        beginKeywords: 'infix infixl infixr',\n        end: '$',\n        contains: [\n          hljs.C_NUMBER_MODE,\n          COMMENT\n        ]\n      },\n      {\n        begin: 'port',\n        end: '$',\n        keywords: 'port',\n        contains: [COMMENT]\n      },\n\n      // Literals and names.\n\n      CHARACTER,\n      hljs.QUOTE_STRING_MODE,\n      hljs.C_NUMBER_MODE,\n      CONSTRUCTOR,\n      hljs.inherit(hljs.TITLE_MODE, {\n        begin: '^[_a-z][\\\\w\\']*'\n      }),\n      COMMENT,\n\n      {\n        begin: '->|<-'\n      } // No markup, relevance booster\n    ],\n    illegal: /;/\n  };\n}\n\nmodule.exports = elm;\n"], "mappings": ";;;;;AAAA;AAAA;AAQA,aAAS,IAAI,MAAM;AACjB,YAAM,UAAU;AAAA,QACd,UAAU;AAAA,UACR,KAAK,QAAQ,MAAM,GAAG;AAAA,UACtB,KAAK;AAAA,YACH;AAAA,YACA;AAAA,YACA;AAAA,cACE,UAAU,CAAC,MAAM;AAAA,YACnB;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAEA,YAAM,cAAc;AAAA,QAClB,WAAW;AAAA,QACX,OAAO;AAAA;AAAA,QACP,WAAW;AAAA,MACb;AAEA,YAAM,OAAO;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,SAAS;AAAA,QACT,UAAU;AAAA,UACR;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,UACT;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAEA,YAAM,SAAS;AAAA,QACb,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU,KAAK;AAAA,MACjB;AAEA,YAAM,YAAY;AAAA,QAChB,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,SAAS;AAAA,MACX;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,UACE;AAAA,QAEF,UAAU;AAAA;AAAA,UAIR;AAAA,YACE,eAAe;AAAA,YACf,KAAK;AAAA,YACL,UAAU;AAAA,YACV,UAAU;AAAA,cACR;AAAA,cACA;AAAA,YACF;AAAA,YACA,SAAS;AAAA,UACX;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU;AAAA,YACV,UAAU;AAAA,cACR;AAAA,cACA;AAAA,YACF;AAAA,YACA,SAAS;AAAA,UACX;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU;AAAA,YACV,UAAU;AAAA,cACR;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE,eAAe;AAAA,YACf,KAAK;AAAA,YACL,UAAU;AAAA,cACR,KAAK;AAAA,cACL;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU;AAAA,YACV,UAAU,CAAC,OAAO;AAAA,UACpB;AAAA;AAAA,UAIA;AAAA,UACA,KAAK;AAAA,UACL,KAAK;AAAA,UACL;AAAA,UACA,KAAK,QAAQ,KAAK,YAAY;AAAA,YAC5B,OAAO;AAAA,UACT,CAAC;AAAA,UACD;AAAA,UAEA;AAAA,YACE,OAAO;AAAA,UACT;AAAA;AAAA,QACF;AAAA,QACA,SAAS;AAAA,MACX;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}