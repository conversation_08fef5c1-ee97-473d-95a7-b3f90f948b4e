<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="/favicon.ico" />
    <%- aceScriptSrc %>
    <title>邮智</title>
    <!-- style -->
    <script>
        fetch('<%- baseUrl %>/api/v1/web/config').then(function (res) {
            return res.json()
        }).then(function (res) {
            console.log('res :>> ', res.data);
            var styleStr = res.data ? res.data.value : ''
            var style = styleStr ? JSON.parse(styleStr) : {
                comp: {},
                bg: ''
            }
            window.ThemeStyle = style
            Object.keys(style.comp).forEach(key => {
                var hsl = style.comp[key]
                var hslStr = `${hsl.h} ${hsl.s * 100}% ${hsl.l * 100}%`
                document.documentElement.style.setProperty(key, hslStr);
            });
        })
    </script>
</head>

<body id='body' style="width: 100%; height:100%">
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div style="width: 100vw; height:100vh" id='root'></div>
    <script type="module" src="/src/index.tsx"></script>
</body>
<script>
    // 触摸板禁止手指缩放
    document.addEventListener('wheel', function (event) {
        if (event.ctrlKey) {
            event.preventDefault()
        }
    }, {
        passive: false
    })
</script>

</html>