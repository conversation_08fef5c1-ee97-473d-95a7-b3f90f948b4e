{"title": "POSTWISE", "prompt": "提示", "login": {"slogen": "便捷、灵活、可靠的企业级大模型应用开发平台", "bottomText": "文擎毕昇", "account": "邮箱", "password": "密码", "confirmPassword": "确认密码", "noAccountRegister": "没有账号？注册", "haveAccountLogin": "已有账号，登录", "loginButton": "登 录", "registerButton": "注 册", "document": "文档", "pleaseEnterAccount": "请填写账号", "pleaseEnterPassword": "请填写密码", "accountTooShort": "账号过短", "passwordTooShort": "请填写密码,至少8位", "passwordError": "密码必须包含大小写字母、数字和字符！", "passwordMismatch": "两次密码不一致", "registrationSuccess": "注册成功,请输入密码进行登录", "pleaseEnterCaptcha": "请输入验证码", "passwordExpired": "您的密码已过期，请及时修改", "otherMethods": "其他登录方式"}, "menu": {"user": "用户", "bookopen": "帮助文档", "github": "GitHub", "workspace": "工作台", "app": "会  话", "skills": "构  建", "knowledge": "知  识", "evaluation": "评  测", "annotation": "标  注", "models": "模  型", "system": "系  统", "log": "审  计", "dataset": "数据集", "themeSwitch": "主题切换", "document": "文档", "logout": "退出", "logoutDescription": "退出登录", "logoutContent": "确认退出登录吗", "forBestExperience": "为了您的良好体验，请在 PC 端访问该网站", "onlineDocumentation": "在线文档", "changePwd": "修改密码"}, "system": {"userManagement": "用户管理", "userGroupsM": "用户组管理", "roleManagement": "角色管理", "systemConfiguration": "系统配置", "username": "用户名", "confirmDisable": "确认禁用该用户？", "roleSelect": "角色选择", "userGroupsSel": "用户组选择", "roleList": "角色列表", "userGroupList": "用户组列表", "confirmText": "是否删除", "roleName": "角色名称", "skillAuthorization": "技能授权", "knowledgeAuthorization": "知识库授权", "skillName": "技能名称", "creator": "创建人", "usePermission": "使用权限", "managePermission": "管理权限", "roleNamePrompt": "角色名称不能超过50字符", "roleNameRequired": "角色名称不可为空", "groupNameExists": "用户组名称不可重复", "groupNamePrompt": "用户组名称不能超过30字符", "groupNameRequired": "用户组名称不可为空", "roleNameExists": "角色名称已存在", "parameterConfig": "参数配置", "language": "语言", "assistantAuthorization": "助手授权", "assistantName": "助手名称", "userList": "用户列表", "userGroup": "用户组", "role": "角色", "searchUserGroups": "搜索用户组", "searchUser": "搜索用户", "searchRoles": "搜索角色", "reset": "重置", "confirm": "确认", "userGroupName": "输入用户组名称", "groupName": "用户组名称", "admins": "管理员", "flowControl": "用户组整体流量控制", "AssistantFlowCtrl": "助手流量控制", "SkillFlowCtrl": "技能流量控制", "flowCtrl": "工作流流量控制", "createdBy": "创建人", "flowCtrlStrategy": "流量控制策略", "limit": "有限制", "unlimited": "无限制", "iconHover": "同时受用户组整体流量控制策略约束", "maximum": "最多", "perMinute": "个同时在线会话", "changeTime": "修改时间", "deleteGroup": "删除后 【{{name}}】 将不再存在，是否删除?", "currentGroup": "当前用户组", "defaultGroup": "默认用户组", "resetPwd": "重置密码", "selectUser": "请选择用户", "selectGroup": "请选择用户组", "selectRole": "请选择角色", "menuAuthorization": "菜单授权", "primaryMenu": "一级菜单", "viewPermission": "查看权限", "themeColor": "主题配色", "toolAuthorization": "工具授权", "createUser": "创建用户", "usernamePlaceholder": "后续使用此用户名进行登录，用户名不可修改", "initialPassword": "初始密码", "passwordPlaceholder": "至少 8 个字符，必须包含大写字母、小写字母、数字和符号的组合", "userGroupRoleSelection": "用户组/角色选择", "usernameRequired": "用户名不可为空", "usernameMaxLength": "用户名最长 30 个字符", "passwordRequirements": "初始密码至少 8 个字符，必须包含大写字母、小写字母、数字和符号的组合", "roleRequired": "至少选择一个角色", "userCreationSuccess": "创建用户成功！已复制用户名和初始密码到剪贴板"}, "skills": {"manageTemplate": "管理技能模板", "createNew": "新建技能", "customSkills": "自定义技能", "chooseOnline": "选择上线版本", "executionSteps": "技能通过可视化的流程编排，明确任务执行步骤", "sceneTemplates": "我们提供场景模板供您使用和参考", "manageProjects": "在此页面管理您的技能，对技能上下线、编辑等等", "skillSearch": "搜索您需要的技能", "confirmDeleteSkill": "确认删除该技能？", "backToSkillList": "返回技能列表", "skillTemplateManagement": "技能模板管理，模板对所有用户可见，支持拖拽排序、删除操作", "templateName": "模板名称", "templateDescription": "模板描述", "confirmText": "是否确认删除该技能模板？", "skillSettings": "技能设置", "basicInfo": "基础信息", "skillName": "技能名称", "description": "描述", "parameterInfo": "参数信息", "advancedConfiguration": "高级配置", "nextStep": "下一步，高级配置", "skillNameRequired": "请填写技能名称", "skillNameTooLong": "技能名称过长，不要超过30字", "skillNameExists": "该名称已存在", "skillDescRequired": "请填写技能描述", "skillDescTooLong": "技能描述过长，不要超过200字", "errorTitle": "关键信息有误", "onlineFailure": "上线失败", "onlineSuccessful": "上线成功", "custom": "自定义", "skillTemplate": "技能模板", "skillTemplateChoose": "您可以从这里挑选一个模板开始，或者自定义高级模板", "createTemplate": "创建模板", "createSuccessTitle": "技能创建成功", "createFailureTitle": "创建失败", "createdBy": "创建用户", "offline": "下线", "online": "上线", "guideWords": "引导词", "currentVersion": "当前版本:", "importLocal": "导入本地组件", "save": "保存", "import": "导入", "export": "导出", "code": "代码", "simplify": "简化", "saveVersion": "另存为新版本", "deleteOrNot": "是否删除", "version": "版本", "saveSuccessful": "保存成功", "supportVersions": "支持分成多个版本分支，分别进行开发以及版本间的比较。", "guideQuestions50": "引导问题最多50个字符", "chatHistoryMaxToken": "聊天历史最大 token 数必须是一个整数", "promptWords1000": "开场白最多为1000个字符", "contactAdmin": "请联系管理员上线助手", "deleteSure": "确认删除该工具？", "modelRequired": "模型为空", "avatar": "技能头像", "switchTo": "切换到"}, "chat": {"newChat": "新建会话", "chooseOne": "选择一个", "dialogue": "对话", "start": "开始", "wenqingruijian": "文擎睿见", "inputPlaceholder": "请输入问题", "uploadFileTooltip": "上传文件", "sendTooltip": "发送", "forms": "表单", "skillTempsTitle": "技能选择", "skillTempsDesc": "选择一个您想使用的线上技能", "networkError": "网络连接出现错误,请尝试以下方法", "networkErrorList1": "操作不要过快", "networkErrorList2": "刷新页面", "networkErrorList3": "检查后台是否启动", "buildError": "您好像缺少了某些配置", "connectionbreakTip": "链接异常断开：", "connectionbreak": "网络断开！", "copyTip": "内容已复制", "noAccess": "因权限不足，该答案剔除了无权查看的内容", "source": "参考来源", "file": "文件", "filePrsing": "文件正在解析中", "sourceTooltip": "来源段落", "filterLabel": "筛选标签", "tooltipText": "系统自动根据答案生成关键信息标签,也可手动增删标签,系统根据标签计算各个文件及段落相关性。", "customLabel": "自定义", "addCustomLabel": "+自定义", "sourceDocumentsLabel": "来源文档", "downloadPDFTooltip": "下载双层PDF", "downloadOriginalTooltip": "下载原文件", "noMatchedFilesMessage": "无匹配的源文件", "fileStorageFailure": " 文件地址失效!", "confirmDeleteChat": "确认删除该会话？", "roundOver": "本轮结束", "chatDialogTip": "设置提示模板中定义的输入变量。与代理和链互动", "feedback": "反馈", "feedbackRequired": "反馈信息不能为空", "dialogueSelection": "对话选择", "chooseSkillOrAssistant": "选择一个您想使用的线上技能或助手", "search": "搜索", "recommendationQuestions": "推荐问题", "historicalMessages": "以上为历史消息", "clickDownload": "点击下载", "searchAssistantOrSkill": "搜索应用", "operationTips": "操作提示：在左侧选择要展示的标签，在右侧拖拽进行排序", "selected": "已选", "pleaseSelectAnApp": "请选择一个应用", "allLabels": "全部标签", "searchLabels": "搜索标签", "confirmed": "已确认", "confirm": "确认", "runNewWorkflow": "开启新会话", "chatEndMessage": "本轮会话已结束"}, "model": {"modelManagement": "模型管理", "modelFineTune": "模型微调", "modelConfiguration": "模型配置", "modelCollectionCaption": "模型集合", "modelName": "模型名称", "status": "状态", "systemModelSettings": "系统模型设置", "addModel": "添加模型", "refresh": "刷新", "serviceProvider": "模型服务提供方", "actions": "操作", "modelType": "模型类型", "onlineOfflineOperation": "上下线操作", "available": "可用", "abnormal": "异常", "unknown": "未知", "empty": "暂无数据", "gpuResourceUsageTitle": "GPU资源使用情况", "jsonFormatError": "JSON格式有误"}, "finetune": {"all": "全部", "successful": "成功", "inProgress": "进行中", "failedAborted": "失败/中止", "modelName": "模型名称", "createTrainingTask": "创建训练任务", "rtService": "FT服务", "rtServiceManagement": "FT服务管理", "createTime": "创建时间", "noData": "暂无数据", "trainingInProgress": "训练中", "trainingFailed": "训练失败", "taskAborted": "任务中止", "trainingSuccess": "训练成功", "publishSuccess": "发布成功", "selectModel": "选择模型以查看详情."}, "flow": {"unsavedChangesConfirmation": "您有未保存的更改，确定要离开吗？", "leave": "离开", "leaveAndSave": "离开并保存", "simplifyConfig": "简化配置", "simplify": "简化", "notifications": "通知", "exit": "退出", "import": "导入", "export": "导出", "code": "代码", "searchComponent": "查找组件", "knowledgeBaseSelection": "知识库选择", "searchKnowledgeBase": "搜索知识库", "minimumParamSetDescription": "您可以在此设置技能所需的最小参数集", "paramList": "参数列表", "saveConfig": "保存配置", "componentLabel": "组件", "aliasLabel": "别名", "editAlias": "修改别名", "parameterLabel": "参数", "notification": "消息", "noNewNotifications": "没有新的通知", "skillName": "技能名", "nameTooLong": "名称过长", "skillDescription": "技能描述", "enterVarName": "请输入变量名", "varNameExists": "变量名重复", "text": "文本", "dropdown": "下拉框", "maxLength": "最大长度", "options": "选项", "variableName": "变量名", "varOptionRequired": "请输入选项内容", "optionRepeated": "选项重复", "incorrectIdFormatMessage": "ID格式有误（包含字符和数字组成的5位字符）", "idAlreadyExistsMessage": "该ID已存在"}, "lib": {"knowledgeBaseId": "知识库ID", "enterLibraryName": "请输入知识库名称", "libraryNameLimit": "知识库名称字数不得超过30字", "selectModel": "请选择一个模型", "nameExists": "该名称已存在", "descriptionLimit": "知识库描述字数不得超过200字", "createLibrary": "创建知识库", "libraryName": "知识库名称", "description": "描述", "model": "模型", "fileData": "文档知识库", "structuredData": "结构化数据", "qaData": "QA知识库", "libraryCollection": "知识库集合", "createUser": "创建用户", "details": "详情", "confirmDeleteLibrary": "确认删除该知识库?", "copy": "复制", "copying": "复制中", "searchPlaceholder": "知识库或文件名称", "desc": "知识库描述", "knowledgeBaseDescription": "请入输入知识库描述", "creationComplete": "完成创建", "embeddingModelSelection": "知识库embedding模型选择"}, "evaluation": {"id": "任务ID", "filename": "测试文件名称", "skillAssistant": "技能助手", "status": "状态", "score": "评测分数", "createDate": "创建日期", "download": "下载", "confirmDeleteEvaluation": "确认删除该评测任务？", "createTitle": "新建任务", "selectLabel": "选择要评测的技能或者助手：", "selectPlaceholder": "请选择", "selectInputPlaceholder": "请根据名称进行搜索", "dataLabel": "测试集数据：", "fileExpandName": "支持扩展名：", "downloadTemplate": "下载模板文件", "promptLabel": "评测指令文本：", "enterExecType": "请选择要评测的技能或助手", "enterUniqueId": "请选择技能或助手ID", "enterVersion": "请选择技能的版本", "enterFile": "请选择测试集数据", "fileSizeLimit": "文件大小限制在10M以内", "enterPrompt": "评测指令不能为空", "evaluationCollection": "评测集合", "tooltip": "该指令文本用于指导大模型对 ground truth 和 answer 提取要点，如无特别需求请勿修改", "create": "创建", "cancel": "取消"}, "code": {"editPythonCodeDescription": "编辑你的 Python 代码此代码片段接受模块导入和一个函数定义。确保您的函数返回一个字符串。", "editCode": "编辑代码", "codeReadyToRun": "代码准备运行", "functionError": "您的函数中存在一个错误", "importsError": "您的导入有误", "errorOccurred": "出错了，请重试", "codeError": "这段代码有问题，请检查以下", "checkAndSave": "检查 & 保存", "export": "导出", "exportToJSON": "导出技能到json文件中", "keyInformationMissing": "您有一些关键信息没有填: ", "skillNameMissing": "请填写技能名称", "useOwnAPIKeys": "使用自己的API keys", "exportSkill": "导出技能", "uploadFile": "上传文件", "clickOrDragHere": "点击或将文件拖拽到这里上传", "dropFileHere": "将文件拖拽到这里上传", "delimiter": "切分符(多个以;分隔)", "splitLength": "切分文本长度", "smartSplit": "智能语义切分", "manualSplit": "手动设置切分", "delimiterPlaceholder": "切分符号", "splitSizePlaceholder": "切分大小", "complete": "完成", "setSplitSize": "请设置文件切分大小", "selectFileToUpload": "请先选择文件上传", "file": "文件", "sizeExceedsLimit": "超过{{size}}M,已移除", "editDictionary": "编辑词典", "exportCodeDialogTip": "生成代码，将流程集成到外部应用程序中 (打开此页面前请先build技能)。", "chunkOverlap": "切分文本重叠长度"}, "report": {"reportTemplate": "报告模板", "reportDescription": "报告生成描述...", "newButton": "新建", "importButton": "导入", "start": "开始", "formSettings": "表单设置", "requiredLabel": "必填", "isRequired": "是必填项", "fileRequired": "当前文件为空", "selectComponent": "选择一个组件", "varLength": "长度不能超过", "requiredField": "{{label}} 为必填项，不能为空。"}, "status": {"1004": "该技能已被删除", "1008": "当前助手或者技能未上线，无法直接对话", "1005": ""}, "build": {"create": "创建", "assistant": "助手", "workflow": "工作流", "skill": "技能", "tools": "工具", "save": "保存", "online": "上线", "offline": "下线", "retry": "重试", "use": "使用", "useAll": "全部使用", "allAppTypes": "全部应用类型", "assistantConfiguration": "助手配置", "createAssistant": "新建助手", "assistantPortrait": "助手画像", "portraitOptimization": "助手画像优化", "automaticOptimization": "自动优化", "createDescription": "通过描述角色和任务来创建你的助手", "nextDescription": "助手可以调用多个技能和工具", "searchAssistant": "搜索您需要的助手", "searchApp": "搜索您需要的应用", "manageAssistant": "在此页面管理您的助手，对助手上下线、编辑等等", "establishAssistant": "创建助手", "assistantName": "助手名称", "giveAssistantName": "给助手取一个名字", "whatWant": "你希望助手的角色是什么，具体完成什么任务？", "example": "示例", "exampleOne": "你是 XX，具有 XX 经验，擅长 XX，…", "exampleTwo": "你的任务是 XX ，需要按照以下步骤执行：", "automaticallyConfigurations": "自动为您选择相关配置", "openingRemarks": "开场白", "guidingQuestions": "引导问题", "promptReplaced": "提示词已替换", "guideReplaced": "引导词已替换", "openingReplaced": "开场白已替换", "toolsReplaced": "工具已替换", "skillsReplaced": "技能已替换", "allReplaced": "已全部替换", "basicConfiguration": "基础配置", "modelConfiguration": "AI模型配置", "model": "模型", "temperature": "温度", "openingIntroduction": "开场引导", "openingStatement": "开场白", "assistantMessageFormat": "助手将在每次对话开始时发送此信息，支持 Markdown 格式", "maximumPromptLength": "开场白最多为1000个字符", "recommendQuestionsForUsers": "为用户提供推荐问题，引导用户提问，超过3个时将随机选取3个", "maxCharacters50": "最多50个字符", "enterGuidingQuestions": "请输入引导问题", "knowledge": "知识", "knowledgeBase": "知识库", "autoCall": "自动调用", "callingMethod": "调用方式", "autoCallDescription": "每轮对话都会对添加的知识库进行检索召回。", "onDemandCall": "按需调用", "onDemandCallDescription": "在助手画像（提示词）中提示调用 RecallKnowledge（可复制）方法，在有需要时才对知识库进行检索。", "createNewKnowledge": "新建知识库", "refresh": "刷新", "abilities": "能力", "skillDescription": "通过可视化界面实现复杂和稳定的业务流程编排，例如项目计划和报告分析", "selectKnowledgeBase": "请选择知识库", "searchBaseName": "搜索知识库名称", "debugPreview": "调试预览", "addTool": "添加工具", "search": "搜索", "empty": "空空如也", "onlineSA": "上线技能&助手", "params": "参数", "added": "已添加", "add": "添加", "configurationUpdated": "配置已更新", "addSkill": "添加技能", "createSkill": "创建技能", "nameRequired": "名称不可为空", "nameMaxLength": "名称最多50个字符", "descMaxLength": "最多1000个字符", "editAssistant": "编辑助手", "enterName": "给助手取一个名字", "assistantDesc": "助手描述", "enterDesc": "介绍助手功能，描述在会话和助手页面可见", "cancel": "取消", "confirm": "确认", "forBetter": "为了更好的助手效果，描述需要大于20 个字", "forExample": "例如助手的身份、完成任务的具体方法和步骤、回答问题时的语气以及应该注意什么问题等", "contentSecurityR": "内容安全审查", "errors": {"selectAtLeastOneWordType": "词表至少需要选择一个", "autoReplyNotEmpty": "自动回复内容不可为空"}, "saveSuccess": "保存成功", "contentSecurity": "内容安全", "contentSecurityDesc": "通过敏感词表或 API 对会话内容进行安全审查", "contentSecuritySettings": "内容安全审查设置", "enableContentSecurityReview": "开启内容安全审查", "reviewType": "审查类型", "sensitiveWordListMatch": "敏感词表匹配", "modelReview": "模型审查", "wordListType": "词表类型", "builtinWordList": "内置词表", "customWordList": "自定义词表", "autoReplyContent": "自动回复内容", "useNewlineToSeparate": "使用换行符进行分隔，每行一个", "txtFile": "txt文件", "sensitiveWordMatch": "敏感词匹配", "defaultAutoReply": "填写命中安全审查时的自动回复内容，例如“当前对话内容违反相关规范，请修改后重新输入。", "uploadAvator": "上传头像", "fileSizeLimit": "文件大小不能超过", "fileTypeLimit": "文件类型不符合要求：", "fieldRequired": "此字段是必填项", "enterApiKey": "请输入API密钥", "enterBaseUrl": "请输入Base URL", "enterProxy": "请输入代理", "enterSubscriptionKey": "请输入订阅密钥", "enterSearchUrl": "请输入搜索URL", "enterDeploymentName": "请输入部署名称", "editTool": "编辑内置工具", "name": "名称", "description": "描述", "dalleStyle": "<PERSON><PERSON>风格", "enterStyle": "请输入风格", "dalleDescription": "<PERSON><PERSON>描述", "enterDescription": "请输入描述", "bingSubscriptionKey": "Bing订阅密钥", "bingSearchUrl": "Bing搜索URL", "enterBingSearchUrl": "请输入Bing搜索URL", "apiKey": "API密钥", "azureEndpoint": "Azure端点", "apiVersion": "API版本", "assistantAvatar": "助手头像", "confirmDeleteSkill": "确认删除该技能?", "confirmDeleteFlow": "确认删除该工作流?", "confirmDeleteAssistant": "确认删除该助手?", "maxToken": "聊天历史最大token数", "maxTokenTip": "通过此参数对聊天历史记录进行裁剪，控制发送给模型的历史消息数量，避免出现超长错误，因此不可大于模型支持的最大上下文长度", "app": "应用", "manageAppTemplates": "管理应用模板", "provideSceneTemplates": "我们提供场景模板供您使用和参考", "noPermissionToPublish": "您没有权限上线此{{type}}，请联系管理员上线。", "manageYourApplications": "在此页面管理您的应用，对应用上下线、编辑等等", "workFlow": "工作流", "skillName": "技能名称", "pleaseFillIn": "请填写{{labelName}}名称", "nameTooLong": "{{labelName}}名称过长，不要超过50字", "addDescription": "加些描述能够快速让别人理解您创造的{{labelName}}", "descriptionTooLong": "{{labelName}}描述不可超过 200 字", "templateCreatedSuccessfully": "模板创建成功", "workFlowName": "工作流名称"}, "tools": {"addTool": "添加工具", "createCustomTool": "API工具", "builtinTools": "内置工具", "customTools": "API工具", "search": "搜索", "empty": "空空如也", "manageCustomTools": "在此页面管理您的自定义工具，对自定义工具创建、编辑等等", "name": "名称", "enterToolName": "输入工具名称", "openapiSchema": "OpenAPI Schema", "enterOpenAPISchema": "输入您的 OpenAPI schema", "importFromUrl": "从 URL 导入", "examples": "示例", "weatherJson": "天气(JSON)", "petShopYaml": "宠物商店(YAML)", "authenticationType": "鉴权方式", "authType": "认证类型", "none": "无", "apiKey": "API Key", "basic": "Basic", "bearer": "Bearer", "availableTools": "可用工具", "description": "描述", "method": "方法", "path": "路径", "delete": "删除", "cancel": "取消", "save": "保存", "toolName": "工具名称"}, "test": {"test": "测试", "addTest": "请先填写测试用例", "uploadTest": "上传测试用例", "explain": "为测试用例是当前组件的输入，只支持 txt 文件，最多 20 行", "testRun": "测试运行", "testCase": "测试用例", "run": "运行", "downloadResults": "下载运行结果", "testCases": "输入测试用例...", "parametersAndValues": "参数和值", "parameter": "参数", "value": "值", "result": "测试结果", "outResultPlaceholder": "点击按钮，输出结果", "maxAddVersions": "最多添加4个版本", "versionEvaluation": "版本评估", "addVersion": "添加版本", "selectVersion": "选择版本", "component": "组件", "parameterName": "参数名", "parameterValue": "参数值"}, "resetPassword": {"slogen": "安全地重置您的密码", "currentPassword": "当前密码", "newPassword": "新密码", "confirmNewPassword": "确认新密码", "pleaseEnterCurrentPassword": "请输入当前密码。", "pleaseEnterNewPassword": "请输入新密码。", "pleaseEnterConfirmPassword": "请确认新密码。", "newPasswordTooShort": "新密码必须至少8个字符。", "passwordMismatch": "新密码不匹配。", "resetButton": "修改密码", "passwordResetSuccess": "您的密码已成功修改", "adminResetSuccess": "密码已重置", "resetFailed": "密码重置失败", "notEmpty": "新密码不能为空"}, "log": {"appUsage": "应用使用", "systemOperations": "系统操作", "auditManagement": "审计管理", "searchButton": "查询", "resetButton": "重置", "auditId": "审计ID", "username": "用户名", "operationTime": "操作时间", "systemModule": "系统模块", "operationAction": "操作行为", "objectType": "操作对象类型", "operationObject": "操作对象", "ipAddress": "IP地址", "remark": "备注", "selectUser": "选择用户", "selectUserGroup": "选择用户组", "startDate": "开始日期", "endDate": "结束日期", "actionBehavior": "操作行为", "createChat": "新建会话", "deleteChat": "删除会话", "createBuild": "新建应用", "updateBuild": "编辑应用", "deleteBuild": "删除应用", "createKnowledge": "新建知识库", "deleteKnowledge": "删除知识库", "uploadFile": "知识库上传文件", "deleteFile": "知识库删除文件", "updateUser": "用户编辑", "forbidUser": "停用用户", "recoverUser": "启用用户", "createUserGroup": "新建用户组", "deleteUserGroup": "删除用户组", "updateUserGroup": "编辑用户组", "createRole": "新建角色", "deleteRole": "删除角色", "updateRole": "编辑角色", "userLogin": "用户登录", "chat": "会话", "build": "构建", "knowledge": "知识库", "system": "系统", "create_chat": "新建会话", "delete_chat": "删除会话", "create_build": "新建应用", "update_build": "编辑应用", "create_knowledge": "新建知识库", "delete_knowledge": "删除知识库", "upload_file": "知识库上传文件", "delete_file": "知识库删除文件", "update_user": "用户编辑", "forbid_user": "停用用户", "recover_user": "启用用户", "create_user_group": "新建用户组", "delete_user_group": "删除用户组", "update_user_group": "编辑用户组", "create_role": "新建角色", "delete_role": "删除角色", "update_role": "编辑角色", "user_login": "用户登录", "none": "无", "flow": "技能", "assistant": "助手", "file": "文件", "user_conf": "用户配置", "user_group_conf": "用户组配置", "role_conf": "角色配置", "appName": "应用名称", "userName": "用户名", "userFeedback": "用户反馈", "enterQuestion": "请先输入问题", "qaLibQuestionAnswerRequired": "QA知识库、问题、答案不能为空", "max100CharsForSimilarQuestions": "相似问最多100个字", "max1000CharsForAnswer": "答案最多1000个字", "saveSuccess": "添加成功", "addNewQaToLib": "添加新的QA到QA知识库", "qaKnowledgeLib": "QA知识库", "question": "问题", "similarQuestions": "相似问题", "aiGenerate": "AI生成", "answer": "答案", "cancel": "取消", "confirm": "确认", "qaLibRequired": "QA知识库不能为空", "selectAtLeastOneQuestion": "至少选一个相似问题", "addSuccess": "添加成功", "addSimilarQuestionsToQaLib": "添加相似问题到QA知识库", "qaContent": "QA 内容", "empty": "空空如也", "selectQaLib": "请选择 QA 知识库", "detailedSession": "详细会话"}, "tag": {"labelMaxLength": "标签名不能超过10个字符", "confirmDeleteLabel": "标签【{{label}}】正在使用中，确认删除？", "createNewLabel": "创建“新标签”", "addLabel": "添加标签"}, "api": {"assistantOrchestration": "助手编排", "skillOrchestration": "技能编排", "externalPublishing": "对外发布", "apiAccess": "API 访问", "noLoginLink": "免登录链接", "loginLink": "需登录链接", "required": "必需", "optional": "可选", "exampleValue": "示例值", "copySuccess": "复制成功", "apiRequestExample": "API 请求示例", "sdkNote": "可以直接使用OpenAI官方SDK中的ChatOpenAI组件去使用助手（只支持文档内有的参数。官方组件里其他的例如n、top_p、max_token等参数暂不支持）", "exampleCode": "示例代码", "requestParams": "请求参数", "bodyParams": "Body 参数", "assistantId": "要使用的助手ID", "messageList": "至今为止对话所包含的消息列表。不支持system类型，system使用助手本身的prompt", "temperature": "使用什么采样温度，介于 0 和 2 之间。非0值会覆盖助手配置", "stream": "默认为 false 如果设置,则像在 ChatGPT 中一样会发送部分消息增量。标记将以仅数据的服务器发送事件的形式发送,这些事件在可用时,并在 data: [DONE] 消息终止流。", "responseData": "返回响应", "dataStructure": "数据结构", "example": "示例", "skillId": "技能ID", "urlParam": "URL传参", "skillInput": "对整个技能的问题输入，json里的具体key和技能本身相关，不一定都是query", "singleInput": "当输入节点只有一个时，id可不传", "historyCount": "对于技能里支持Memery，选取几条历史消息进行多轮问答，默认值10", "clearCache": "是否清除session缓存", "sessionId": "用于session查找", "sessionRemark": "每次调用，当session_id 传入时，返回传入sessionid，当session_id不传时，自动生成id", "tweaks": "对每个组件的控制，可以替换组件输入参数的值", "tweaksRemark": "当没有指定组件传参的时候，可以不传", "exampleComponent": "示例，技能中OpenAI大模型组件的配置信息，key为组件名，命名为{组件}-{id}", "defaultConfig": "当{}为空，表示保持默认值", "componentParams": "每个技能中各个组件的参数均可以在调用接口时传进去，如果不传则用技能的默认配置", "returnContent": "返回内容", "sessionIdReturn": "会话id，用来和入参对应", "skillResult": "技能返回的结果", "llmAnswer": "技能统一key返回的LLM 内容", "messageId": "技能历史消息存储id", "source": "是否溯源", "dynamicKey": "key是技能里组件定义的输出key，输出的内容和answer一致，唯一的区别是key不固定", "useCase": "应用案例", "knowledgeQADemo": "知识库问答应用示例", "specifyKnowledgeBase": "如需指定知识库进行问答，可在传入参数时指定知识库 id，例如：", "reportGenerationDemo": "报告生成应用示例", "step1": "step1：确认触发该技能执行的依赖项", "dependenciesDescription": "依赖项即对应界面上会话页面中创建会话时左下角表单中的内容，对应技能编辑时使用的组件", "step2": "step2：准备入参", "uploadFiles": "本示例中入参为 2 个文件：", "step3": "step3：组装 tweaks", "step4": "step4：执行技能", "noLoginLinkDescription": "免登录链接无需登录即可使用，仅在系统配置 enable_guest_access = True 时可访问免登录链接", "loginLinkDescription": "需登录链接需要在登录后使用", "publishAsStandalonePage": "发布为独立页面", "copyLinkToBrowser": "复制链接到浏览器中打开", "embedIntoWebsite": "嵌入到网站中", "styleOne": "样式一", "styleTwo": "样式二", "embedCodeDescription": "将以下代码嵌入到网站中"}, "dataset": {"confirmDelete": "确认删除数据集！", "fileNotFound": "文件不存在", "name": "数据集名称", "create": "创建数据集", "creationTime": "创建时间", "updateTime": "更新时间", "createUser": "创建者", "download": "下载", "collection": "数据集集合", "enterDataSetName": "请填写数据集名称", "maxDataSetNameLength": "数据集名称最多 30 个字", "uploadFile": "请上传文件", "selectKnowledgeLib": "请选择知识库", "creationSuccess": "数据集创建成功", "createDataset": "创建数据集", "importMethod": "导入方式", "localImport": "本地导入", "importFromQa": "从QA知识库导入", "sampleFile": "示例文件", "jsonSample": "json格式示例.json", "selectQaKnowledgeLib": "选择QA知识库"}, "label": {"createTask": "创建标注任务", "selectAppsToLabel": "选择要标注的应用", "unmarkedConversationCount": "当前未标注会话数", "selectLabelers": "选择标注人", "cancel": "取消", "create": "创建", "confirmDelete": "您确定要删除此任务吗？", "delete": "删除", "taskId": "任务 ID", "taskStatus": "任务状态", "creationTime": "创建时间", "createdBy": "创建人", "labelingProgress": "标注进度", "actions": "操作", "view": "查看", "noData": "暂无数据", "all": "全部", "notStarted": "未开始", "completed": "已完成", "inProgress": "进行中", "taskStatusFilter": "任务状态筛选", "maxAppsError": "最多选择30个应用", "createSuccess": "创建成功", "back": "返回", "appName": "应用名称", "sessionCreationTime": "会话创建时间", "userFeedback": "用户反馈", "annotationStatus": "标注状态", "unannotated": "未标注", "annotated": "已标注", "noAnnotationRequired": "无需标注", "annotator": "标注人", "returnToList": "返回列表", "unlabeled": "未标注", "labeled": "已标注", "unnecessary": "无需标注", "previousChat": "上一条会话", "nextChat": "下一条会话", "selectPlaceholder": "请选择", "searchAppsPlaceholder": "搜索应用名称"}, "errors": {"10400": "助手不存在", "10401": "助手上线失败", "10402": "助手名称重复", "10403": "助手已上线，不可编辑", "10410": "工具名称已存在", "10411": "工具下的API不能为空", "10412": "工具不存在", "10413": "预置工具类别不可删除", "403": "暂无操作权限", "404": "资源不存在", "10300": "组件已存在", "10301": "组件不存在", "10100": "创建训练任务失败", "10101": "个人训练集和预置训练集最少选择一个", "10102": "任务不存在", "10103": "任务状态错误", "10104": "任务取消失败", "10105": "任务删除失败", "10106": "任务发布失败", "10107": "模型名接口修改失败", "10108": "取消发布失败", "10109": "无效的训练参数", "10110": "模型名已存在", "10120": "训练文件不存在", "10125": "获取GPU信息失败", "10126": "获取模型列表失败", "10500": "未找到技能版本信息", "10501": "当前正在使用版本无法删除", "10502": "版本名已存在", "10503": "技能名称重复", "10520": "技能不存在", "10521": "技能已上线，不可编辑", "10525": "工作流已上线，不可编辑", "10529": "工作流名称重复", "10530": "模板名称已存在", "10900": "知识库名称不可重复", "10901": "知识库必须选择一个embedding模型", "10910": "当前知识库版本不支持修改分段，请创建新知识库后进行分段修改", "10800": "模型服务提供方名称重复，请修改", "10801": "模型不可重复", "10700": "标签已存在", "10701": "未找到对应的标签", "10600": "账号或密码错误", "10601": "您的密码已过期，请及时修改", "10602": "用户尚未设置密码，请先联系管理员重置密码", "10603": "当前密码错误", "10604": "您的账户已在另一设备上登录，此设备上的会话已被注销。\n如果这不是您本人的操作，请尽快修改您的账户密码。", "10605": "用户名已存在", "10606": "用户组和角色不能为空", "10610": "用户组内还有用户，不能删除", "10920": "未配置QA知识库相似问模型", "10930": "该问题已存在", "10527": "工作流等待用户输入超时", "10528": "{{type}}节点执行超过最大次数", "10531": "{{type}}功能已升级,需删除后重新拖入", "10532": "工作流版本已升级,请联系创建者重新编排", "10540": "服务器线程数已满，请稍候再试"}, "all": "全部", "confirmButton": "确定", "add": "添加", "back": "返回", "create": "创建", "createImport": "创建并导入", "delete": "删除", "deleteSuccess": "删除成功", "createTime": "创建时间", "updateTime": "更新时间", "saved": "保存成功", "edit": "编辑", "enable": "启用", "disable": "禁用", "close": "关闭", "cancel": "取消", "save": "保存", "submit": "提交", "operations": "操作", "previousPage": "上一页", "nextPage": "下一页", "formatError": "格式错误", "port": "服务端口", "cancle": "取消", "tip": "提示", "search": "搜索", "deleteAssistant": "确认删除该助手？", "chatTipsTitle": "使用提示", "updateSuccess": "修改成功", "createSuccess": "创建成功", "confirm": "确认", "required": "不可为空", "inProgress": "进行中", "failed": "失败", "success": "成功", "addSimilarQuestion": "添加相似问题", "addQa": "添加QA", "inserVar": "插入变量", "chatTips": "1. 请遵守《中华人民共和国网络安全法》《中华人民共和国数据安全法》《中华人民共和国个人信息保护法》及《生成式人工智能服务安全基本要求》；\n2. 禁止上传涉及公司保密管理规定范围内的保密数据；\n3. 禁止上传包含个人隐私信息的数据；\n4. 禁止上传包含恶意程序或病毒的文件；\n5. 禁止使用 AI 生成攻击性代码；\n6. 禁止向 AI 提问包括但不限于涉爆涉恐涉黄及危害国家安全及公共安全的违法不良信息；\n7. AI 生成内容受到公司保密管理规定约束。用户应根据实际情况判断生成内容的合理性，避免 AI 幻觉造成的风险。"}