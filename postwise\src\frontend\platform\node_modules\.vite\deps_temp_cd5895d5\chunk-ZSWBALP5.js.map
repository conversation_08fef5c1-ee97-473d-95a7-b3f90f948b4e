{"version": 3, "sources": ["../../highlight.js/lib/languages/mojolicious.js"], "sourcesContent": ["/*\nLanguage: Mojolicious\nRequires: xml.js, perl.js\nAuthor: <PERSON><PERSON> <<EMAIL>>\nDescription: Mojolicious .ep (Embedded Perl) templates\nWebsite: https://mojolicious.org\nCategory: template\n*/\nfunction mojolicious(hljs) {\n  return {\n    name: 'Mojolicious',\n    subLanguage: 'xml',\n    contains: [\n      {\n        className: 'meta',\n        begin: '^__(END|DATA)__$'\n      },\n      // mojolicious line\n      {\n        begin: \"^\\\\s*%{1,2}={0,2}\",\n        end: '$',\n        subLanguage: 'perl'\n      },\n      // mojolicious block\n      {\n        begin: \"<%{1,2}={0,2}\",\n        end: \"={0,1}%>\",\n        subLanguage: 'perl',\n        excludeBegin: true,\n        excludeEnd: true\n      }\n    ]\n  };\n}\n\nmodule.exports = mojolicious;\n"], "mappings": ";;;;;AAAA;AAAA;AAQA,aAAS,YAAY,MAAM;AACzB,aAAO;AAAA,QACL,MAAM;AAAA,QACN,aAAa;AAAA,QACb,UAAU;AAAA,UACR;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,UACT;AAAA;AAAA,UAEA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,aAAa;AAAA,UACf;AAAA;AAAA,UAEA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,aAAa;AAAA,YACb,cAAc;AAAA,YACd,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}