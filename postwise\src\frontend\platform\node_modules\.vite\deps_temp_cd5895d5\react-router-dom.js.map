{"version": 3, "sources": ["../../react-router-dom/dom.ts", "../../react-router-dom/index.tsx"], "sourcesContent": ["import type {\n  FormEncType,\n  HTMLFormMethod,\n  RelativeRoutingType,\n} from \"@remix-run/router\";\nimport { stripBasename, UNSAFE_warning as warning } from \"@remix-run/router\";\n\nexport const defaultMethod: HTMLFormMethod = \"get\";\nconst defaultEncType: FormEncType = \"application/x-www-form-urlencoded\";\n\nexport function isHtmlElement(object: any): object is HTMLElement {\n  return object != null && typeof object.tagName === \"string\";\n}\n\nexport function isButtonElement(object: any): object is HTMLButtonElement {\n  return isHtmlElement(object) && object.tagName.toLowerCase() === \"button\";\n}\n\nexport function isFormElement(object: any): object is HTMLFormElement {\n  return isHtmlElement(object) && object.tagName.toLowerCase() === \"form\";\n}\n\nexport function isInputElement(object: any): object is HTMLInputElement {\n  return isHtmlElement(object) && object.tagName.toLowerCase() === \"input\";\n}\n\ntype LimitedMouseEvent = Pick<\n  MouseEvent,\n  \"button\" | \"metaKey\" | \"altKey\" | \"ctrlKey\" | \"shiftKey\"\n>;\n\nfunction isModifiedEvent(event: LimitedMouseEvent) {\n  return !!(event.metaKey || event.altKey || event.ctrlKey || event.shiftKey);\n}\n\nexport function shouldProcessLinkClick(\n  event: LimitedMouseEvent,\n  target?: string\n) {\n  return (\n    event.button === 0 && // Ignore everything but left clicks\n    (!target || target === \"_self\") && // Let browser handle \"target=_blank\" etc.\n    !isModifiedEvent(event) // Ignore clicks with modifier keys\n  );\n}\n\nexport type ParamKeyValuePair = [string, string];\n\nexport type URLSearchParamsInit =\n  | string\n  | ParamKeyValuePair[]\n  | Record<string, string | string[]>\n  | URLSearchParams;\n\n/**\n * Creates a URLSearchParams object using the given initializer.\n *\n * This is identical to `new URLSearchParams(init)` except it also\n * supports arrays as values in the object form of the initializer\n * instead of just strings. This is convenient when you need multiple\n * values for a given key, but don't want to use an array initializer.\n *\n * For example, instead of:\n *\n *   let searchParams = new URLSearchParams([\n *     ['sort', 'name'],\n *     ['sort', 'price']\n *   ]);\n *\n * you can do:\n *\n *   let searchParams = createSearchParams({\n *     sort: ['name', 'price']\n *   });\n */\nexport function createSearchParams(\n  init: URLSearchParamsInit = \"\"\n): URLSearchParams {\n  return new URLSearchParams(\n    typeof init === \"string\" ||\n    Array.isArray(init) ||\n    init instanceof URLSearchParams\n      ? init\n      : Object.keys(init).reduce((memo, key) => {\n          let value = init[key];\n          return memo.concat(\n            Array.isArray(value) ? value.map((v) => [key, v]) : [[key, value]]\n          );\n        }, [] as ParamKeyValuePair[])\n  );\n}\n\nexport function getSearchParamsForLocation(\n  locationSearch: string,\n  defaultSearchParams: URLSearchParams | null\n) {\n  let searchParams = createSearchParams(locationSearch);\n\n  if (defaultSearchParams) {\n    // Use `defaultSearchParams.forEach(...)` here instead of iterating of\n    // `defaultSearchParams.keys()` to work-around a bug in Firefox related to\n    // web extensions. Relevant Bugzilla tickets:\n    // https://bugzilla.mozilla.org/show_bug.cgi?id=1414602\n    // https://bugzilla.mozilla.org/show_bug.cgi?id=1023984\n    defaultSearchParams.forEach((_, key) => {\n      if (!searchParams.has(key)) {\n        defaultSearchParams.getAll(key).forEach((value) => {\n          searchParams.append(key, value);\n        });\n      }\n    });\n  }\n\n  return searchParams;\n}\n\n// Thanks https://github.com/sindresorhus/type-fest!\ntype JsonObject = { [Key in string]: JsonValue } & {\n  [Key in string]?: JsonValue | undefined;\n};\ntype JsonArray = JsonValue[] | readonly JsonValue[];\ntype JsonPrimitive = string | number | boolean | null;\ntype JsonValue = JsonPrimitive | JsonObject | JsonArray;\n\nexport type SubmitTarget =\n  | HTMLFormElement\n  | HTMLButtonElement\n  | HTMLInputElement\n  | FormData\n  | URLSearchParams\n  | JsonValue\n  | null;\n\n// One-time check for submitter support\nlet _formDataSupportsSubmitter: boolean | null = null;\n\nfunction isFormDataSubmitterSupported() {\n  if (_formDataSupportsSubmitter === null) {\n    try {\n      new FormData(\n        document.createElement(\"form\"),\n        // @ts-expect-error if FormData supports the submitter parameter, this will throw\n        0\n      );\n      _formDataSupportsSubmitter = false;\n    } catch (e) {\n      _formDataSupportsSubmitter = true;\n    }\n  }\n  return _formDataSupportsSubmitter;\n}\n\nexport interface SubmitOptions {\n  /**\n   * The HTTP method used to submit the form. Overrides `<form method>`.\n   * Defaults to \"GET\".\n   */\n  method?: HTMLFormMethod;\n\n  /**\n   * The action URL path used to submit the form. Overrides `<form action>`.\n   * Defaults to the path of the current route.\n   */\n  action?: string;\n\n  /**\n   * The encoding used to submit the form. Overrides `<form encType>`.\n   * Defaults to \"application/x-www-form-urlencoded\".\n   */\n  encType?: FormEncType;\n\n  /**\n   * Set `true` to replace the current entry in the browser's history stack\n   * instead of creating a new one (i.e. stay on \"the same page\"). Defaults\n   * to `false`.\n   */\n  replace?: boolean;\n\n  /**\n   * State object to add to the history stack entry for this navigation\n   */\n  state?: any;\n\n  /**\n   * Determines whether the form action is relative to the route hierarchy or\n   * the pathname.  Use this if you want to opt out of navigating the route\n   * hierarchy and want to instead route based on /-delimited URL segments\n   */\n  relative?: RelativeRoutingType;\n\n  /**\n   * In browser-based environments, prevent resetting scroll after this\n   * navigation when using the <ScrollRestoration> component\n   */\n  preventScrollReset?: boolean;\n\n  /**\n   * Enable view transitions on this submission navigation\n   */\n  unstable_viewTransition?: boolean;\n}\n\nconst supportedFormEncTypes: Set<FormEncType> = new Set([\n  \"application/x-www-form-urlencoded\",\n  \"multipart/form-data\",\n  \"text/plain\",\n]);\n\nfunction getFormEncType(encType: string | null) {\n  if (encType != null && !supportedFormEncTypes.has(encType as FormEncType)) {\n    warning(\n      false,\n      `\"${encType}\" is not a valid \\`encType\\` for \\`<Form>\\`/\\`<fetcher.Form>\\` ` +\n        `and will default to \"${defaultEncType}\"`\n    );\n\n    return null;\n  }\n  return encType;\n}\n\nexport function getFormSubmissionInfo(\n  target: SubmitTarget,\n  basename: string\n): {\n  action: string | null;\n  method: string;\n  encType: string;\n  formData: FormData | undefined;\n  body: any;\n} {\n  let method: string;\n  let action: string | null;\n  let encType: string;\n  let formData: FormData | undefined;\n  let body: any;\n\n  if (isFormElement(target)) {\n    // When grabbing the action from the element, it will have had the basename\n    // prefixed to ensure non-JS scenarios work, so strip it since we'll\n    // re-prefix in the router\n    let attr = target.getAttribute(\"action\");\n    action = attr ? stripBasename(attr, basename) : null;\n    method = target.getAttribute(\"method\") || defaultMethod;\n    encType = getFormEncType(target.getAttribute(\"enctype\")) || defaultEncType;\n\n    formData = new FormData(target);\n  } else if (\n    isButtonElement(target) ||\n    (isInputElement(target) &&\n      (target.type === \"submit\" || target.type === \"image\"))\n  ) {\n    let form = target.form;\n\n    if (form == null) {\n      throw new Error(\n        `Cannot submit a <button> or <input type=\"submit\"> without a <form>`\n      );\n    }\n\n    // <button>/<input type=\"submit\"> may override attributes of <form>\n\n    // When grabbing the action from the element, it will have had the basename\n    // prefixed to ensure non-JS scenarios work, so strip it since we'll\n    // re-prefix in the router\n    let attr = target.getAttribute(\"formaction\") || form.getAttribute(\"action\");\n    action = attr ? stripBasename(attr, basename) : null;\n\n    method =\n      target.getAttribute(\"formmethod\") ||\n      form.getAttribute(\"method\") ||\n      defaultMethod;\n    encType =\n      getFormEncType(target.getAttribute(\"formenctype\")) ||\n      getFormEncType(form.getAttribute(\"enctype\")) ||\n      defaultEncType;\n\n    // Build a FormData object populated from a form and submitter\n    formData = new FormData(form, target);\n\n    // If this browser doesn't support the `FormData(el, submitter)` format,\n    // then tack on the submitter value at the end.  This is a lightweight\n    // solution that is not 100% spec compliant.  For complete support in older\n    // browsers, consider using the `formdata-submitter-polyfill` package\n    if (!isFormDataSubmitterSupported()) {\n      let { name, type, value } = target;\n      if (type === \"image\") {\n        let prefix = name ? `${name}.` : \"\";\n        formData.append(`${prefix}x`, \"0\");\n        formData.append(`${prefix}y`, \"0\");\n      } else if (name) {\n        formData.append(name, value);\n      }\n    }\n  } else if (isHtmlElement(target)) {\n    throw new Error(\n      `Cannot submit element that is not <form>, <button>, or ` +\n        `<input type=\"submit|image\">`\n    );\n  } else {\n    method = defaultMethod;\n    action = null;\n    encType = defaultEncType;\n    body = target;\n  }\n\n  // Send body for <Form encType=\"text/plain\" so we encode it into text\n  if (formData && encType === \"text/plain\") {\n    body = formData;\n    formData = undefined;\n  }\n\n  return { action, method: method.toLowerCase(), encType, formData, body };\n}\n", "/**\n * NOTE: If you refactor this to split up the modules into separate files,\n * you'll need to update the rollup config for react-router-dom-v5-compat.\n */\nimport * as React from \"react\";\nimport type {\n  DataRouteObject,\n  FutureConfig,\n  Location,\n  NavigateOptions,\n  NavigationType,\n  Navigator,\n  RelativeRoutingType,\n  RouteObject,\n  RouterProviderProps,\n  To,\n} from \"react-router\";\nimport {\n  Router,\n  createPath,\n  useHref,\n  useLocation,\n  useMatches,\n  useNavigate,\n  useNavigation,\n  useResolvedPath,\n  unstable_useBlocker as useBlocker,\n  UNSAFE_DataRouterContext as DataRouterContext,\n  UNSAFE_DataRouterStateContext as DataRouterStateContext,\n  UNSAFE_NavigationContext as NavigationContext,\n  UNSAFE_RouteContext as RouteContext,\n  UNSAFE_mapRouteProperties as mapRouteProperties,\n  UNSAFE_useRouteId as useRouteId,\n  UNSAFE_useRoutesImpl as useRoutesImpl,\n} from \"react-router\";\nimport type {\n  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON>er,\n  <PERSON>EncType,\n  FormMethod,\n  FutureConfig as RouterFutureConfig,\n  GetScrollRestorationKeyFunction,\n  HashHistory,\n  History,\n  HTMLFormMethod,\n  HydrationState,\n  Router as RemixRouter,\n  V7_FormMethod,\n  RouterState,\n  RouterSubscriber,\n} from \"@remix-run/router\";\nimport {\n  createRouter,\n  createBrowserHistory,\n  createHashHistory,\n  joinPaths,\n  stripBasename,\n  UNSAFE_ErrorResponseImpl as ErrorResponseImpl,\n  UNSAFE_invariant as invariant,\n  UNSAFE_warning as warning,\n  matchPath,\n} from \"@remix-run/router\";\n\nimport type {\n  SubmitOptions,\n  ParamKeyValuePair,\n  URLSearchParamsInit,\n  SubmitTarget,\n} from \"./dom\";\nimport {\n  createSearchParams,\n  defaultMethod,\n  getFormSubmissionInfo,\n  getSearchParamsForLocation,\n  shouldProcessLinkClick,\n} from \"./dom\";\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Re-exports\n////////////////////////////////////////////////////////////////////////////////\n\nexport type {\n  FormEncType,\n  FormMethod,\n  GetScrollRestorationKeyFunction,\n  ParamKeyValuePair,\n  SubmitOptions,\n  URLSearchParamsInit,\n  V7_FormMethod,\n};\nexport { createSearchParams };\n\n// Note: Keep in sync with react-router exports!\nexport type {\n  ActionFunction,\n  ActionFunctionArgs,\n  AwaitProps,\n  unstable_Blocker,\n  unstable_BlockerFunction,\n  DataRouteMatch,\n  DataRouteObject,\n  ErrorResponse,\n  Fetcher,\n  Hash,\n  IndexRouteObject,\n  IndexRouteProps,\n  JsonFunction,\n  LazyRouteFunction,\n  LayoutRouteProps,\n  LoaderFunction,\n  LoaderFunctionArgs,\n  Location,\n  MemoryRouterProps,\n  NavigateFunction,\n  NavigateOptions,\n  NavigateProps,\n  Navigation,\n  Navigator,\n  NonIndexRouteObject,\n  OutletProps,\n  Params,\n  ParamParseKey,\n  Path,\n  PathMatch,\n  Pathname,\n  PathPattern,\n  PathRouteProps,\n  RedirectFunction,\n  RelativeRoutingType,\n  RouteMatch,\n  RouteObject,\n  RouteProps,\n  RouterProps,\n  RouterProviderProps,\n  RoutesProps,\n  Search,\n  ShouldRevalidateFunction,\n  ShouldRevalidateFunctionArgs,\n  To,\n  UIMatch,\n} from \"react-router\";\nexport {\n  AbortedDeferredError,\n  Await,\n  MemoryRouter,\n  Navigate,\n  NavigationType,\n  Outlet,\n  Route,\n  Router,\n  Routes,\n  createMemoryRouter,\n  createPath,\n  createRoutesFromChildren,\n  createRoutesFromElements,\n  defer,\n  isRouteErrorResponse,\n  generatePath,\n  json,\n  matchPath,\n  matchRoutes,\n  parsePath,\n  redirect,\n  redirectDocument,\n  renderMatches,\n  resolvePath,\n  useActionData,\n  useAsyncError,\n  useAsyncValue,\n  unstable_useBlocker,\n  useHref,\n  useInRouterContext,\n  useLoaderData,\n  useLocation,\n  useMatch,\n  useMatches,\n  useNavigate,\n  useNavigation,\n  useNavigationType,\n  useOutlet,\n  useOutletContext,\n  useParams,\n  useResolvedPath,\n  useRevalidator,\n  useRouteError,\n  useRouteLoaderData,\n  useRoutes,\n} from \"react-router\";\n\n///////////////////////////////////////////////////////////////////////////////\n// DANGER! PLEASE READ ME!\n// We provide these exports as an escape hatch in the event that you need any\n// routing data that we don't provide an explicit API for. With that said, we\n// want to cover your use case if we can, so if you feel the need to use these\n// we want to hear from you. Let us know what you're building and we'll do our\n// best to make sure we can support you!\n//\n// We consider these exports an implementation detail and do not guarantee\n// against any breaking changes, regardless of the semver release. Use with\n// extreme caution and only if you understand the consequences. Godspeed.\n///////////////////////////////////////////////////////////////////////////////\n\n/** @internal */\nexport {\n  UNSAFE_DataRouterContext,\n  UNSAFE_DataRouterStateContext,\n  UNSAFE_NavigationContext,\n  UNSAFE_LocationContext,\n  UNSAFE_RouteContext,\n  UNSAFE_useRouteId,\n} from \"react-router\";\n//#endregion\n\ndeclare global {\n  var __staticRouterHydrationData: HydrationState | undefined;\n  interface Document {\n    startViewTransition(cb: () => Promise<void> | void): ViewTransition;\n  }\n}\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Routers\n////////////////////////////////////////////////////////////////////////////////\n\ninterface DOMRouterOpts {\n  basename?: string;\n  future?: Partial<Omit<RouterFutureConfig, \"v7_prependBasename\">>;\n  hydrationData?: HydrationState;\n  window?: Window;\n}\n\nexport function createBrowserRouter(\n  routes: RouteObject[],\n  opts?: DOMRouterOpts\n): RemixRouter {\n  return createRouter({\n    basename: opts?.basename,\n    future: {\n      ...opts?.future,\n      v7_prependBasename: true,\n    },\n    history: createBrowserHistory({ window: opts?.window }),\n    hydrationData: opts?.hydrationData || parseHydrationData(),\n    routes,\n    mapRouteProperties,\n    window: opts?.window,\n  }).initialize();\n}\n\nexport function createHashRouter(\n  routes: RouteObject[],\n  opts?: DOMRouterOpts\n): RemixRouter {\n  return createRouter({\n    basename: opts?.basename,\n    future: {\n      ...opts?.future,\n      v7_prependBasename: true,\n    },\n    history: createHashHistory({ window: opts?.window }),\n    hydrationData: opts?.hydrationData || parseHydrationData(),\n    routes,\n    mapRouteProperties,\n    window: opts?.window,\n  }).initialize();\n}\n\nfunction parseHydrationData(): HydrationState | undefined {\n  let state = window?.__staticRouterHydrationData;\n  if (state && state.errors) {\n    state = {\n      ...state,\n      errors: deserializeErrors(state.errors),\n    };\n  }\n  return state;\n}\n\nfunction deserializeErrors(\n  errors: RemixRouter[\"state\"][\"errors\"]\n): RemixRouter[\"state\"][\"errors\"] {\n  if (!errors) return null;\n  let entries = Object.entries(errors);\n  let serialized: RemixRouter[\"state\"][\"errors\"] = {};\n  for (let [key, val] of entries) {\n    // Hey you!  If you change this, please change the corresponding logic in\n    // serializeErrors in react-router-dom/server.tsx :)\n    if (val && val.__type === \"RouteErrorResponse\") {\n      serialized[key] = new ErrorResponseImpl(\n        val.status,\n        val.statusText,\n        val.data,\n        val.internal === true\n      );\n    } else if (val && val.__type === \"Error\") {\n      // Attempt to reconstruct the right type of Error (i.e., ReferenceError)\n      if (val.__subType) {\n        let ErrorConstructor = window[val.__subType];\n        if (typeof ErrorConstructor === \"function\") {\n          try {\n            // @ts-expect-error\n            let error = new ErrorConstructor(val.message);\n            // Wipe away the client-side stack trace.  Nothing to fill it in with\n            // because we don't serialize SSR stack traces for security reasons\n            error.stack = \"\";\n            serialized[key] = error;\n          } catch (e) {\n            // no-op - fall through and create a normal Error\n          }\n        }\n      }\n\n      if (serialized[key] == null) {\n        let error = new Error(val.message);\n        // Wipe away the client-side stack trace.  Nothing to fill it in with\n        // because we don't serialize SSR stack traces for security reasons\n        error.stack = \"\";\n        serialized[key] = error;\n      }\n    } else {\n      serialized[key] = val;\n    }\n  }\n  return serialized;\n}\n\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Contexts\n////////////////////////////////////////////////////////////////////////////////\n\ntype ViewTransitionContextObject =\n  | {\n      isTransitioning: false;\n    }\n  | {\n      isTransitioning: true;\n      currentLocation: Location;\n      nextLocation: Location;\n    };\n\nconst ViewTransitionContext = React.createContext<ViewTransitionContextObject>({\n  isTransitioning: false,\n});\nif (__DEV__) {\n  ViewTransitionContext.displayName = \"ViewTransition\";\n}\n\nexport { ViewTransitionContext as UNSAFE_ViewTransitionContext };\n\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Components\n////////////////////////////////////////////////////////////////////////////////\n\n/**\n  Webpack + React 17 fails to compile on any of the following because webpack\n  complains that `startTransition` doesn't exist in `React`:\n  * import { startTransition } from \"react\"\n  * import * as React from from \"react\";\n    \"startTransition\" in React ? React.startTransition(() => setState()) : setState()\n  * import * as React from from \"react\";\n    \"startTransition\" in React ? React[\"startTransition\"](() => setState()) : setState()\n\n  Moving it to a constant such as the following solves the Webpack/React 17 issue:\n  * import * as React from from \"react\";\n    const START_TRANSITION = \"startTransition\";\n    START_TRANSITION in React ? React[START_TRANSITION](() => setState()) : setState()\n\n  However, that introduces webpack/terser minification issues in production builds\n  in React 18 where minification/obfuscation ends up removing the call of\n  React.startTransition entirely from the first half of the ternary.  Grabbing\n  this exported reference once up front resolves that issue.\n\n  See https://github.com/remix-run/react-router/issues/10579\n*/\nconst START_TRANSITION = \"startTransition\";\nconst startTransitionImpl = React[START_TRANSITION];\n\nfunction startTransitionSafe(cb: () => void) {\n  if (startTransitionImpl) {\n    startTransitionImpl(cb);\n  } else {\n    cb();\n  }\n}\n\ninterface ViewTransition {\n  finished: Promise<void>;\n  ready: Promise<void>;\n  updateCallbackDone: Promise<void>;\n  skipTransition(): void;\n}\n\nclass Deferred<T> {\n  status: \"pending\" | \"resolved\" | \"rejected\" = \"pending\";\n  promise: Promise<T>;\n  // @ts-expect-error - no initializer\n  resolve: (value: T) => void;\n  // @ts-expect-error - no initializer\n  reject: (reason?: unknown) => void;\n  constructor() {\n    this.promise = new Promise((resolve, reject) => {\n      this.resolve = (value) => {\n        if (this.status === \"pending\") {\n          this.status = \"resolved\";\n          resolve(value);\n        }\n      };\n      this.reject = (reason) => {\n        if (this.status === \"pending\") {\n          this.status = \"rejected\";\n          reject(reason);\n        }\n      };\n    });\n  }\n}\n\n/**\n * Given a Remix Router instance, render the appropriate UI\n */\nexport function RouterProvider({\n  fallbackElement,\n  router,\n  future,\n}: RouterProviderProps): React.ReactElement {\n  let [state, setStateImpl] = React.useState(router.state);\n  let [pendingState, setPendingState] = React.useState<RouterState>();\n  let [vtContext, setVtContext] = React.useState<ViewTransitionContextObject>({\n    isTransitioning: false,\n  });\n  let [renderDfd, setRenderDfd] = React.useState<Deferred<void>>();\n  let [transition, setTransition] = React.useState<ViewTransition>();\n  let [interruption, setInterruption] = React.useState<{\n    state: RouterState;\n    currentLocation: Location;\n    nextLocation: Location;\n  }>();\n  let { v7_startTransition } = future || {};\n\n  let optInStartTransition = React.useCallback(\n    (cb: () => void) => {\n      if (v7_startTransition) {\n        startTransitionSafe(cb);\n      } else {\n        cb();\n      }\n    },\n    [v7_startTransition]\n  );\n\n  let setState = React.useCallback<RouterSubscriber>(\n    (\n      newState: RouterState,\n      { unstable_viewTransitionOpts: viewTransitionOpts }\n    ) => {\n      if (\n        !viewTransitionOpts ||\n        router.window == null ||\n        typeof router.window.document.startViewTransition !== \"function\"\n      ) {\n        // Mid-navigation state update, or startViewTransition isn't available\n        optInStartTransition(() => setStateImpl(newState));\n      } else if (transition && renderDfd) {\n        // Interrupting an in-progress transition, cancel and let everything flush\n        // out, and then kick off a new transition from the interruption state\n        renderDfd.resolve();\n        transition.skipTransition();\n        setInterruption({\n          state: newState,\n          currentLocation: viewTransitionOpts.currentLocation,\n          nextLocation: viewTransitionOpts.nextLocation,\n        });\n      } else {\n        // Completed navigation update with opted-in view transitions, let 'er rip\n        setPendingState(newState);\n        setVtContext({\n          isTransitioning: true,\n          currentLocation: viewTransitionOpts.currentLocation,\n          nextLocation: viewTransitionOpts.nextLocation,\n        });\n      }\n    },\n    [optInStartTransition, transition, renderDfd, router.window]\n  );\n\n  // Need to use a layout effect here so we are subscribed early enough to\n  // pick up on any render-driven redirects/navigations (useEffect/<Navigate>)\n  React.useLayoutEffect(() => router.subscribe(setState), [router, setState]);\n\n  // When we start a view transition, create a Deferred we can use for the\n  // eventual \"completed\" render\n  React.useEffect(() => {\n    if (vtContext.isTransitioning) {\n      setRenderDfd(new Deferred<void>());\n    }\n  }, [vtContext.isTransitioning]);\n\n  // Once the deferred is created, kick off startViewTransition() to update the\n  // DOM and then wait on the Deferred to resolve (indicating the DOM update has\n  // happened)\n  React.useEffect(() => {\n    if (renderDfd && pendingState && router.window) {\n      let newState = pendingState;\n      let renderPromise = renderDfd.promise;\n      let transition = router.window.document.startViewTransition(async () => {\n        optInStartTransition(() => setStateImpl(newState));\n        await renderPromise;\n      });\n      transition.finished.finally(() => {\n        setRenderDfd(undefined);\n        setTransition(undefined);\n        setPendingState(undefined);\n        setVtContext({ isTransitioning: false });\n      });\n      setTransition(transition);\n    }\n  }, [optInStartTransition, pendingState, renderDfd, router.window]);\n\n  // When the new location finally renders and is committed to the DOM, this\n  // effect will run to resolve the transition\n  React.useEffect(() => {\n    if (\n      renderDfd &&\n      pendingState &&\n      state.location.key === pendingState.location.key\n    ) {\n      renderDfd.resolve();\n    }\n  }, [renderDfd, transition, state.location, pendingState]);\n\n  // If we get interrupted with a new navigation during a transition, we skip\n  // the active transition, let it cleanup, then kick it off again here\n  React.useEffect(() => {\n    if (!vtContext.isTransitioning && interruption) {\n      setPendingState(interruption.state);\n      setVtContext({\n        isTransitioning: true,\n        currentLocation: interruption.currentLocation,\n        nextLocation: interruption.nextLocation,\n      });\n      setInterruption(undefined);\n    }\n  }, [vtContext.isTransitioning, interruption]);\n\n  let navigator = React.useMemo((): Navigator => {\n    return {\n      createHref: router.createHref,\n      encodeLocation: router.encodeLocation,\n      go: (n) => router.navigate(n),\n      push: (to, state, opts) =>\n        router.navigate(to, {\n          state,\n          preventScrollReset: opts?.preventScrollReset,\n        }),\n      replace: (to, state, opts) =>\n        router.navigate(to, {\n          replace: true,\n          state,\n          preventScrollReset: opts?.preventScrollReset,\n        }),\n    };\n  }, [router]);\n\n  let basename = router.basename || \"/\";\n\n  let dataRouterContext = React.useMemo(\n    () => ({\n      router,\n      navigator,\n      static: false,\n      basename,\n    }),\n    [router, navigator, basename]\n  );\n\n  // The fragment and {null} here are important!  We need them to keep React 18's\n  // useId happy when we are server-rendering since we may have a <script> here\n  // containing the hydrated server-side staticContext (from StaticRouterProvider).\n  // useId relies on the component tree structure to generate deterministic id's\n  // so we need to ensure it remains the same on the client even though\n  // we don't need the <script> tag\n  return (\n    <>\n      <DataRouterContext.Provider value={dataRouterContext}>\n        <DataRouterStateContext.Provider value={state}>\n          <ViewTransitionContext.Provider value={vtContext}>\n            <Router\n              basename={basename}\n              location={state.location}\n              navigationType={state.historyAction}\n              navigator={navigator}\n            >\n              {state.initialized ? (\n                <DataRoutes routes={router.routes} state={state} />\n              ) : (\n                fallbackElement\n              )}\n            </Router>\n          </ViewTransitionContext.Provider>\n        </DataRouterStateContext.Provider>\n      </DataRouterContext.Provider>\n      {null}\n    </>\n  );\n}\n\nfunction DataRoutes({\n  routes,\n  state,\n}: {\n  routes: DataRouteObject[];\n  state: RouterState;\n}): React.ReactElement | null {\n  return useRoutesImpl(routes, undefined, state);\n}\n\nexport interface BrowserRouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  future?: FutureConfig;\n  window?: Window;\n}\n\n/**\n * A `<Router>` for use in web browsers. Provides the cleanest URLs.\n */\nexport function BrowserRouter({\n  basename,\n  children,\n  future,\n  window,\n}: BrowserRouterProps) {\n  let historyRef = React.useRef<BrowserHistory>();\n  if (historyRef.current == null) {\n    historyRef.current = createBrowserHistory({ window, v5Compat: true });\n  }\n\n  let history = historyRef.current;\n  let [state, setStateImpl] = React.useState({\n    action: history.action,\n    location: history.location,\n  });\n  let { v7_startTransition } = future || {};\n  let setState = React.useCallback(\n    (newState: { action: NavigationType; location: Location }) => {\n      v7_startTransition && startTransitionImpl\n        ? startTransitionImpl(() => setStateImpl(newState))\n        : setStateImpl(newState);\n    },\n    [setStateImpl, v7_startTransition]\n  );\n\n  React.useLayoutEffect(() => history.listen(setState), [history, setState]);\n\n  return (\n    <Router\n      basename={basename}\n      children={children}\n      location={state.location}\n      navigationType={state.action}\n      navigator={history}\n    />\n  );\n}\n\nexport interface HashRouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  future?: FutureConfig;\n  window?: Window;\n}\n\n/**\n * A `<Router>` for use in web browsers. Stores the location in the hash\n * portion of the URL so it is not sent to the server.\n */\nexport function HashRouter({\n  basename,\n  children,\n  future,\n  window,\n}: HashRouterProps) {\n  let historyRef = React.useRef<HashHistory>();\n  if (historyRef.current == null) {\n    historyRef.current = createHashHistory({ window, v5Compat: true });\n  }\n\n  let history = historyRef.current;\n  let [state, setStateImpl] = React.useState({\n    action: history.action,\n    location: history.location,\n  });\n  let { v7_startTransition } = future || {};\n  let setState = React.useCallback(\n    (newState: { action: NavigationType; location: Location }) => {\n      v7_startTransition && startTransitionImpl\n        ? startTransitionImpl(() => setStateImpl(newState))\n        : setStateImpl(newState);\n    },\n    [setStateImpl, v7_startTransition]\n  );\n\n  React.useLayoutEffect(() => history.listen(setState), [history, setState]);\n\n  return (\n    <Router\n      basename={basename}\n      children={children}\n      location={state.location}\n      navigationType={state.action}\n      navigator={history}\n    />\n  );\n}\n\nexport interface HistoryRouterProps {\n  basename?: string;\n  children?: React.ReactNode;\n  future?: FutureConfig;\n  history: History;\n}\n\n/**\n * A `<Router>` that accepts a pre-instantiated history object. It's important\n * to note that using your own history object is highly discouraged and may add\n * two versions of the history library to your bundles unless you use the same\n * version of the history library that React Router uses internally.\n */\nfunction HistoryRouter({\n  basename,\n  children,\n  future,\n  history,\n}: HistoryRouterProps) {\n  let [state, setStateImpl] = React.useState({\n    action: history.action,\n    location: history.location,\n  });\n  let { v7_startTransition } = future || {};\n  let setState = React.useCallback(\n    (newState: { action: NavigationType; location: Location }) => {\n      v7_startTransition && startTransitionImpl\n        ? startTransitionImpl(() => setStateImpl(newState))\n        : setStateImpl(newState);\n    },\n    [setStateImpl, v7_startTransition]\n  );\n\n  React.useLayoutEffect(() => history.listen(setState), [history, setState]);\n\n  return (\n    <Router\n      basename={basename}\n      children={children}\n      location={state.location}\n      navigationType={state.action}\n      navigator={history}\n    />\n  );\n}\n\nif (__DEV__) {\n  HistoryRouter.displayName = \"unstable_HistoryRouter\";\n}\n\nexport { HistoryRouter as unstable_HistoryRouter };\n\nexport interface LinkProps\n  extends Omit<React.AnchorHTMLAttributes<HTMLAnchorElement>, \"href\"> {\n  reloadDocument?: boolean;\n  replace?: boolean;\n  state?: any;\n  preventScrollReset?: boolean;\n  relative?: RelativeRoutingType;\n  to: To;\n  unstable_viewTransition?: boolean;\n}\n\nconst isBrowser =\n  typeof window !== \"undefined\" &&\n  typeof window.document !== \"undefined\" &&\n  typeof window.document.createElement !== \"undefined\";\n\nconst ABSOLUTE_URL_REGEX = /^(?:[a-z][a-z0-9+.-]*:|\\/\\/)/i;\n\n/**\n * The public API for rendering a history-aware `<a>`.\n */\nexport const Link = React.forwardRef<HTMLAnchorElement, LinkProps>(\n  function LinkWithRef(\n    {\n      onClick,\n      relative,\n      reloadDocument,\n      replace,\n      state,\n      target,\n      to,\n      preventScrollReset,\n      unstable_viewTransition,\n      ...rest\n    },\n    ref\n  ) {\n    let { basename } = React.useContext(NavigationContext);\n\n    // Rendered into <a href> for absolute URLs\n    let absoluteHref;\n    let isExternal = false;\n\n    if (typeof to === \"string\" && ABSOLUTE_URL_REGEX.test(to)) {\n      // Render the absolute href server- and client-side\n      absoluteHref = to;\n\n      // Only check for external origins client-side\n      if (isBrowser) {\n        try {\n          let currentUrl = new URL(window.location.href);\n          let targetUrl = to.startsWith(\"//\")\n            ? new URL(currentUrl.protocol + to)\n            : new URL(to);\n          let path = stripBasename(targetUrl.pathname, basename);\n\n          if (targetUrl.origin === currentUrl.origin && path != null) {\n            // Strip the protocol/origin/basename for same-origin absolute URLs\n            to = path + targetUrl.search + targetUrl.hash;\n          } else {\n            isExternal = true;\n          }\n        } catch (e) {\n          // We can't do external URL detection without a valid URL\n          warning(\n            false,\n            `<Link to=\"${to}\"> contains an invalid URL which will probably break ` +\n              `when clicked - please update to a valid URL path.`\n          );\n        }\n      }\n    }\n\n    // Rendered into <a href> for relative URLs\n    let href = useHref(to, { relative });\n\n    let internalOnClick = useLinkClickHandler(to, {\n      replace,\n      state,\n      target,\n      preventScrollReset,\n      relative,\n      unstable_viewTransition,\n    });\n    function handleClick(\n      event: React.MouseEvent<HTMLAnchorElement, MouseEvent>\n    ) {\n      if (onClick) onClick(event);\n      if (!event.defaultPrevented) {\n        internalOnClick(event);\n      }\n    }\n\n    return (\n      // eslint-disable-next-line jsx-a11y/anchor-has-content\n      <a\n        {...rest}\n        href={absoluteHref || href}\n        onClick={isExternal || reloadDocument ? onClick : handleClick}\n        ref={ref}\n        target={target}\n      />\n    );\n  }\n);\n\nif (__DEV__) {\n  Link.displayName = \"Link\";\n}\n\ntype NavLinkRenderProps = {\n  isActive: boolean;\n  isPending: boolean;\n  isTransitioning: boolean;\n};\n\nexport interface NavLinkProps\n  extends Omit<LinkProps, \"className\" | \"style\" | \"children\"> {\n  children?: React.ReactNode | ((props: NavLinkRenderProps) => React.ReactNode);\n  caseSensitive?: boolean;\n  className?: string | ((props: NavLinkRenderProps) => string | undefined);\n  end?: boolean;\n  style?:\n    | React.CSSProperties\n    | ((props: NavLinkRenderProps) => React.CSSProperties | undefined);\n  unstable_viewTransition?: boolean;\n}\n\n/**\n * A `<Link>` wrapper that knows if it's \"active\" or not.\n */\nexport const NavLink = React.forwardRef<HTMLAnchorElement, NavLinkProps>(\n  function NavLinkWithRef(\n    {\n      \"aria-current\": ariaCurrentProp = \"page\",\n      caseSensitive = false,\n      className: classNameProp = \"\",\n      end = false,\n      style: styleProp,\n      to,\n      unstable_viewTransition,\n      children,\n      ...rest\n    },\n    ref\n  ) {\n    let path = useResolvedPath(to, { relative: rest.relative });\n    let location = useLocation();\n    let routerState = React.useContext(DataRouterStateContext);\n    let { navigator } = React.useContext(NavigationContext);\n    let isTransitioning =\n      routerState != null &&\n      // Conditional usage is OK here because the usage of a data router is static\n      // eslint-disable-next-line react-hooks/rules-of-hooks\n      useViewTransitionState(path) &&\n      unstable_viewTransition === true;\n\n    let toPathname = navigator.encodeLocation\n      ? navigator.encodeLocation(path).pathname\n      : path.pathname;\n    let locationPathname = location.pathname;\n    let nextLocationPathname =\n      routerState && routerState.navigation && routerState.navigation.location\n        ? routerState.navigation.location.pathname\n        : null;\n\n    if (!caseSensitive) {\n      locationPathname = locationPathname.toLowerCase();\n      nextLocationPathname = nextLocationPathname\n        ? nextLocationPathname.toLowerCase()\n        : null;\n      toPathname = toPathname.toLowerCase();\n    }\n\n    let isActive =\n      locationPathname === toPathname ||\n      (!end &&\n        locationPathname.startsWith(toPathname) &&\n        locationPathname.charAt(toPathname.length) === \"/\");\n\n    let isPending =\n      nextLocationPathname != null &&\n      (nextLocationPathname === toPathname ||\n        (!end &&\n          nextLocationPathname.startsWith(toPathname) &&\n          nextLocationPathname.charAt(toPathname.length) === \"/\"));\n\n    let renderProps = {\n      isActive,\n      isPending,\n      isTransitioning,\n    };\n\n    let ariaCurrent = isActive ? ariaCurrentProp : undefined;\n\n    let className: string | undefined;\n    if (typeof classNameProp === \"function\") {\n      className = classNameProp(renderProps);\n    } else {\n      // If the className prop is not a function, we use a default `active`\n      // class for <NavLink />s that are active. In v5 `active` was the default\n      // value for `activeClassName`, but we are removing that API and can still\n      // use the old default behavior for a cleaner upgrade path and keep the\n      // simple styling rules working as they currently do.\n      className = [\n        classNameProp,\n        isActive ? \"active\" : null,\n        isPending ? \"pending\" : null,\n        isTransitioning ? \"transitioning\" : null,\n      ]\n        .filter(Boolean)\n        .join(\" \");\n    }\n\n    let style =\n      typeof styleProp === \"function\" ? styleProp(renderProps) : styleProp;\n\n    return (\n      <Link\n        {...rest}\n        aria-current={ariaCurrent}\n        className={className}\n        ref={ref}\n        style={style}\n        to={to}\n        unstable_viewTransition={unstable_viewTransition}\n      >\n        {typeof children === \"function\" ? children(renderProps) : children}\n      </Link>\n    );\n  }\n);\n\nif (__DEV__) {\n  NavLink.displayName = \"NavLink\";\n}\n\nexport interface FetcherFormProps\n  extends React.FormHTMLAttributes<HTMLFormElement> {\n  /**\n   * The HTTP verb to use when the form is submit. Supports \"get\", \"post\",\n   * \"put\", \"delete\", \"patch\".\n   */\n  method?: HTMLFormMethod;\n\n  /**\n   * `<form encType>` - enhancing beyond the normal string type and limiting\n   * to the built-in browser supported values\n   */\n  encType?:\n    | \"application/x-www-form-urlencoded\"\n    | \"multipart/form-data\"\n    | \"text/plain\";\n\n  /**\n   * Normal `<form action>` but supports React Router's relative paths.\n   */\n  action?: string;\n\n  /**\n   * Determines whether the form action is relative to the route hierarchy or\n   * the pathname.  Use this if you want to opt out of navigating the route\n   * hierarchy and want to instead route based on /-delimited URL segments\n   */\n  relative?: RelativeRoutingType;\n\n  /**\n   * Prevent the scroll position from resetting to the top of the viewport on\n   * completion of the navigation when using the <ScrollRestoration> component\n   */\n  preventScrollReset?: boolean;\n\n  /**\n   * A function to call when the form is submitted. If you call\n   * `event.preventDefault()` then this form will not do anything.\n   */\n  onSubmit?: React.FormEventHandler<HTMLFormElement>;\n}\n\nexport interface FormProps extends FetcherFormProps {\n  /**\n   * Forces a full document navigation instead of a fetch.\n   */\n  reloadDocument?: boolean;\n\n  /**\n   * Replaces the current entry in the browser history stack when the form\n   * navigates. Use this if you don't want the user to be able to click \"back\"\n   * to the page with the form on it.\n   */\n  replace?: boolean;\n\n  /**\n   * State object to add to the history stack entry for this navigation\n   */\n  state?: any;\n\n  /**\n   * Enable view transitions on this Form navigation\n   */\n  unstable_viewTransition?: boolean;\n}\n\n/**\n * A `@remix-run/router`-aware `<form>`. It behaves like a normal form except\n * that the interaction with the server is with `fetch` instead of new document\n * requests, allowing components to add nicer UX to the page as the form is\n * submitted and returns with data.\n */\nexport const Form = React.forwardRef<HTMLFormElement, FormProps>(\n  (props, ref) => {\n    let submit = useSubmit();\n    return <FormImpl {...props} submit={submit} ref={ref} />;\n  }\n);\n\nif (__DEV__) {\n  Form.displayName = \"Form\";\n}\n\ntype HTMLSubmitEvent = React.BaseSyntheticEvent<\n  SubmitEvent,\n  Event,\n  HTMLFormElement\n>;\n\ntype HTMLFormSubmitter = HTMLButtonElement | HTMLInputElement;\n\ninterface FormImplProps extends FormProps {\n  submit: SubmitFunction | FetcherSubmitFunction;\n}\n\nconst FormImpl = React.forwardRef<HTMLFormElement, FormImplProps>(\n  (\n    {\n      reloadDocument,\n      replace,\n      state,\n      method = defaultMethod,\n      action,\n      onSubmit,\n      submit,\n      relative,\n      preventScrollReset,\n      unstable_viewTransition,\n      ...props\n    },\n    forwardedRef\n  ) => {\n    let formMethod: HTMLFormMethod =\n      method.toLowerCase() === \"get\" ? \"get\" : \"post\";\n    let formAction = useFormAction(action, { relative });\n    let submitHandler: React.FormEventHandler<HTMLFormElement> = (event) => {\n      onSubmit && onSubmit(event);\n      if (event.defaultPrevented) return;\n      event.preventDefault();\n\n      let submitter = (event as unknown as HTMLSubmitEvent).nativeEvent\n        .submitter as HTMLFormSubmitter | null;\n\n      let submitMethod =\n        (submitter?.getAttribute(\"formmethod\") as HTMLFormMethod | undefined) ||\n        method;\n\n      submit(submitter || event.currentTarget, {\n        method: submitMethod,\n        replace,\n        state,\n        relative,\n        preventScrollReset,\n        unstable_viewTransition,\n      });\n    };\n\n    return (\n      <form\n        ref={forwardedRef}\n        method={formMethod}\n        action={formAction}\n        onSubmit={reloadDocument ? onSubmit : submitHandler}\n        {...props}\n      />\n    );\n  }\n);\n\nif (__DEV__) {\n  FormImpl.displayName = \"FormImpl\";\n}\n\nexport interface ScrollRestorationProps {\n  getKey?: GetScrollRestorationKeyFunction;\n  storageKey?: string;\n}\n\n/**\n * This component will emulate the browser's scroll restoration on location\n * changes.\n */\nexport function ScrollRestoration({\n  getKey,\n  storageKey,\n}: ScrollRestorationProps) {\n  useScrollRestoration({ getKey, storageKey });\n  return null;\n}\n\nif (__DEV__) {\n  ScrollRestoration.displayName = \"ScrollRestoration\";\n}\n//#endregion\n\n////////////////////////////////////////////////////////////////////////////////\n//#region Hooks\n////////////////////////////////////////////////////////////////////////////////\n\nenum DataRouterHook {\n  UseScrollRestoration = \"useScrollRestoration\",\n  UseSubmit = \"useSubmit\",\n  UseSubmitFetcher = \"useSubmitFetcher\",\n  UseFetcher = \"useFetcher\",\n  useViewTransitionState = \"useViewTransitionState\",\n}\n\nenum DataRouterStateHook {\n  UseFetchers = \"useFetchers\",\n  UseScrollRestoration = \"useScrollRestoration\",\n}\n\nfunction getDataRouterConsoleError(\n  hookName: DataRouterHook | DataRouterStateHook\n) {\n  return `${hookName} must be used within a data router.  See https://reactrouter.com/routers/picking-a-router.`;\n}\n\nfunction useDataRouterContext(hookName: DataRouterHook) {\n  let ctx = React.useContext(DataRouterContext);\n  invariant(ctx, getDataRouterConsoleError(hookName));\n  return ctx;\n}\n\nfunction useDataRouterState(hookName: DataRouterStateHook) {\n  let state = React.useContext(DataRouterStateContext);\n  invariant(state, getDataRouterConsoleError(hookName));\n  return state;\n}\n\n/**\n * Handles the click behavior for router `<Link>` components. This is useful if\n * you need to create custom `<Link>` components with the same click behavior we\n * use in our exported `<Link>`.\n */\nexport function useLinkClickHandler<E extends Element = HTMLAnchorElement>(\n  to: To,\n  {\n    target,\n    replace: replaceProp,\n    state,\n    preventScrollReset,\n    relative,\n    unstable_viewTransition,\n  }: {\n    target?: React.HTMLAttributeAnchorTarget;\n    replace?: boolean;\n    state?: any;\n    preventScrollReset?: boolean;\n    relative?: RelativeRoutingType;\n    unstable_viewTransition?: boolean;\n  } = {}\n): (event: React.MouseEvent<E, MouseEvent>) => void {\n  let navigate = useNavigate();\n  let location = useLocation();\n  let path = useResolvedPath(to, { relative });\n\n  return React.useCallback(\n    (event: React.MouseEvent<E, MouseEvent>) => {\n      if (shouldProcessLinkClick(event, target)) {\n        event.preventDefault();\n\n        // If the URL hasn't changed, a regular <a> will do a replace instead of\n        // a push, so do the same here unless the replace prop is explicitly set\n        let replace =\n          replaceProp !== undefined\n            ? replaceProp\n            : createPath(location) === createPath(path);\n\n        navigate(to, {\n          replace,\n          state,\n          preventScrollReset,\n          relative,\n          unstable_viewTransition,\n        });\n      }\n    },\n    [\n      location,\n      navigate,\n      path,\n      replaceProp,\n      state,\n      target,\n      to,\n      preventScrollReset,\n      relative,\n      unstable_viewTransition,\n    ]\n  );\n}\n\n/**\n * A convenient wrapper for reading and writing search parameters via the\n * URLSearchParams interface.\n */\nexport function useSearchParams(\n  defaultInit?: URLSearchParamsInit\n): [URLSearchParams, SetURLSearchParams] {\n  warning(\n    typeof URLSearchParams !== \"undefined\",\n    `You cannot use the \\`useSearchParams\\` hook in a browser that does not ` +\n      `support the URLSearchParams API. If you need to support Internet ` +\n      `Explorer 11, we recommend you load a polyfill such as ` +\n      `https://github.com/ungap/url-search-params\\n\\n` +\n      `If you're unsure how to load polyfills, we recommend you check out ` +\n      `https://polyfill.io/v3/ which provides some recommendations about how ` +\n      `to load polyfills only for users that need them, instead of for every ` +\n      `user.`\n  );\n\n  let defaultSearchParamsRef = React.useRef(createSearchParams(defaultInit));\n  let hasSetSearchParamsRef = React.useRef(false);\n\n  let location = useLocation();\n  let searchParams = React.useMemo(\n    () =>\n      // Only merge in the defaults if we haven't yet called setSearchParams.\n      // Once we call that we want those to take precedence, otherwise you can't\n      // remove a param with setSearchParams({}) if it has an initial value\n      getSearchParamsForLocation(\n        location.search,\n        hasSetSearchParamsRef.current ? null : defaultSearchParamsRef.current\n      ),\n    [location.search]\n  );\n\n  let navigate = useNavigate();\n  let setSearchParams = React.useCallback<SetURLSearchParams>(\n    (nextInit, navigateOptions) => {\n      const newSearchParams = createSearchParams(\n        typeof nextInit === \"function\" ? nextInit(searchParams) : nextInit\n      );\n      hasSetSearchParamsRef.current = true;\n      navigate(\"?\" + newSearchParams, navigateOptions);\n    },\n    [navigate, searchParams]\n  );\n\n  return [searchParams, setSearchParams];\n}\n\nexport type SetURLSearchParams = (\n  nextInit?:\n    | URLSearchParamsInit\n    | ((prev: URLSearchParams) => URLSearchParamsInit),\n  navigateOpts?: NavigateOptions\n) => void;\n\n/**\n * Submits a HTML `<form>` to the server without reloading the page.\n */\nexport interface SubmitFunction {\n  (\n    /**\n     * Specifies the `<form>` to be submitted to the server, a specific\n     * `<button>` or `<input type=\"submit\">` to use to submit the form, or some\n     * arbitrary data to submit.\n     *\n     * Note: When using a `<button>` its `name` and `value` will also be\n     * included in the form data that is submitted.\n     */\n    target: SubmitTarget,\n\n    /**\n     * Options that override the `<form>`'s own attributes. Required when\n     * submitting arbitrary data without a backing `<form>`.\n     */\n    options?: SubmitOptions\n  ): void;\n}\n\n/**\n * Submits a fetcher `<form>` to the server without reloading the page.\n */\nexport interface FetcherSubmitFunction {\n  (\n    target: SubmitTarget,\n    // Fetchers cannot replace or set state because they are not navigation events\n    options?: Omit<SubmitOptions, \"replace\" | \"state\">\n  ): void;\n}\n\nfunction validateClientSideSubmission() {\n  if (typeof document === \"undefined\") {\n    throw new Error(\n      \"You are calling submit during the server render. \" +\n        \"Try calling submit within a `useEffect` or callback instead.\"\n    );\n  }\n}\n\n/**\n * Returns a function that may be used to programmatically submit a form (or\n * some arbitrary data) to the server.\n */\nexport function useSubmit(): SubmitFunction {\n  let { router } = useDataRouterContext(DataRouterHook.UseSubmit);\n  let { basename } = React.useContext(NavigationContext);\n  let currentRouteId = useRouteId();\n\n  return React.useCallback<SubmitFunction>(\n    (target, options = {}) => {\n      validateClientSideSubmission();\n\n      let { action, method, encType, formData, body } = getFormSubmissionInfo(\n        target,\n        basename\n      );\n\n      router.navigate(options.action || action, {\n        preventScrollReset: options.preventScrollReset,\n        formData,\n        body,\n        formMethod: options.method || (method as HTMLFormMethod),\n        formEncType: options.encType || (encType as FormEncType),\n        replace: options.replace,\n        state: options.state,\n        fromRouteId: currentRouteId,\n        unstable_viewTransition: options.unstable_viewTransition,\n      });\n    },\n    [router, basename, currentRouteId]\n  );\n}\n\n/**\n * Returns the implementation for fetcher.submit\n */\nfunction useSubmitFetcher(\n  fetcherKey: string,\n  fetcherRouteId: string\n): FetcherSubmitFunction {\n  let { router } = useDataRouterContext(DataRouterHook.UseSubmitFetcher);\n  let { basename } = React.useContext(NavigationContext);\n\n  return React.useCallback<FetcherSubmitFunction>(\n    (target, options = {}) => {\n      validateClientSideSubmission();\n\n      let { action, method, encType, formData, body } = getFormSubmissionInfo(\n        target,\n        basename\n      );\n\n      invariant(\n        fetcherRouteId != null,\n        \"No routeId available for useFetcher()\"\n      );\n      router.fetch(fetcherKey, fetcherRouteId, options.action || action, {\n        preventScrollReset: options.preventScrollReset,\n        formData,\n        body,\n        formMethod: options.method || (method as HTMLFormMethod),\n        formEncType: options.encType || (encType as FormEncType),\n      });\n    },\n    [router, basename, fetcherKey, fetcherRouteId]\n  );\n}\n\n// v7: Eventually we should deprecate this entirely in favor of using the\n// router method directly?\nexport function useFormAction(\n  action?: string,\n  { relative }: { relative?: RelativeRoutingType } = {}\n): string {\n  let { basename } = React.useContext(NavigationContext);\n  let routeContext = React.useContext(RouteContext);\n  invariant(routeContext, \"useFormAction must be used inside a RouteContext\");\n\n  let [match] = routeContext.matches.slice(-1);\n  // Shallow clone path so we can modify it below, otherwise we modify the\n  // object referenced by useMemo inside useResolvedPath\n  let path = { ...useResolvedPath(action ? action : \".\", { relative }) };\n\n  // Previously we set the default action to \".\". The problem with this is that\n  // `useResolvedPath(\".\")` excludes search params of the resolved URL. This is\n  // the intended behavior of when \".\" is specifically provided as\n  // the form action, but inconsistent w/ browsers when the action is omitted.\n  // https://github.com/remix-run/remix/issues/927\n  let location = useLocation();\n  if (action == null) {\n    // Safe to write to this directly here since if action was undefined, we\n    // would have called useResolvedPath(\".\") which will never include a search\n    path.search = location.search;\n\n    // When grabbing search params from the URL, remove the automatically\n    // inserted ?index param so we match the useResolvedPath search behavior\n    // which would not include ?index\n    if (match.route.index) {\n      let params = new URLSearchParams(path.search);\n      params.delete(\"index\");\n      path.search = params.toString() ? `?${params.toString()}` : \"\";\n    }\n  }\n\n  if ((!action || action === \".\") && match.route.index) {\n    path.search = path.search\n      ? path.search.replace(/^\\?/, \"?index&\")\n      : \"?index\";\n  }\n\n  // If we're operating within a basename, prepend it to the pathname prior\n  // to creating the form action.  If this is a root navigation, then just use\n  // the raw basename which allows the basename to have full control over the\n  // presence of a trailing slash on root actions\n  if (basename !== \"/\") {\n    path.pathname =\n      path.pathname === \"/\" ? basename : joinPaths([basename, path.pathname]);\n  }\n\n  return createPath(path);\n}\n\nfunction createFetcherForm(fetcherKey: string, routeId: string) {\n  let FetcherForm = React.forwardRef<HTMLFormElement, FetcherFormProps>(\n    (props, ref) => {\n      let submit = useSubmitFetcher(fetcherKey, routeId);\n      return <FormImpl {...props} ref={ref} submit={submit} />;\n    }\n  );\n  if (__DEV__) {\n    FetcherForm.displayName = \"fetcher.Form\";\n  }\n  return FetcherForm;\n}\n\nlet fetcherId = 0;\n\nexport type FetcherWithComponents<TData> = Fetcher<TData> & {\n  Form: ReturnType<typeof createFetcherForm>;\n  submit: FetcherSubmitFunction;\n  load: (href: string) => void;\n};\n\n// TODO: (v7) Change the useFetcher generic default from `any` to `unknown`\n\n/**\n * Interacts with route loaders and actions without causing a navigation. Great\n * for any interaction that stays on the same page.\n */\nexport function useFetcher<TData = any>(): FetcherWithComponents<TData> {\n  let { router } = useDataRouterContext(DataRouterHook.UseFetcher);\n\n  let route = React.useContext(RouteContext);\n  invariant(route, `useFetcher must be used inside a RouteContext`);\n\n  let routeId = route.matches[route.matches.length - 1]?.route.id;\n  invariant(\n    routeId != null,\n    `useFetcher can only be used on routes that contain a unique \"id\"`\n  );\n\n  let [fetcherKey] = React.useState(() => String(++fetcherId));\n  let [Form] = React.useState(() => {\n    invariant(routeId, `No routeId available for fetcher.Form()`);\n    return createFetcherForm(fetcherKey, routeId);\n  });\n  let [load] = React.useState(() => (href: string) => {\n    invariant(router, \"No router available for fetcher.load()\");\n    invariant(routeId, \"No routeId available for fetcher.load()\");\n    router.fetch(fetcherKey, routeId, href);\n  });\n  let submit = useSubmitFetcher(fetcherKey, routeId);\n\n  let fetcher = router.getFetcher<TData>(fetcherKey);\n\n  let fetcherWithComponents = React.useMemo(\n    () => ({\n      Form,\n      submit,\n      load,\n      ...fetcher,\n    }),\n    [fetcher, Form, submit, load]\n  );\n\n  React.useEffect(() => {\n    // Is this busted when the React team gets real weird and calls effects\n    // twice on mount?  We really just need to garbage collect here when this\n    // fetcher is no longer around.\n    return () => {\n      if (!router) {\n        console.warn(`No router available to clean up from useFetcher()`);\n        return;\n      }\n      router.deleteFetcher(fetcherKey);\n    };\n  }, [router, fetcherKey]);\n\n  return fetcherWithComponents;\n}\n\n/**\n * Provides all fetchers currently on the page. Useful for layouts and parent\n * routes that need to provide pending/optimistic UI regarding the fetch.\n */\nexport function useFetchers(): Fetcher[] {\n  let state = useDataRouterState(DataRouterStateHook.UseFetchers);\n  return [...state.fetchers.values()];\n}\n\nconst SCROLL_RESTORATION_STORAGE_KEY = \"react-router-scroll-positions\";\nlet savedScrollPositions: Record<string, number> = {};\n\n/**\n * When rendered inside a RouterProvider, will restore scroll positions on navigations\n */\nfunction useScrollRestoration({\n  getKey,\n  storageKey,\n}: {\n  getKey?: GetScrollRestorationKeyFunction;\n  storageKey?: string;\n} = {}) {\n  let { router } = useDataRouterContext(DataRouterHook.UseScrollRestoration);\n  let { restoreScrollPosition, preventScrollReset } = useDataRouterState(\n    DataRouterStateHook.UseScrollRestoration\n  );\n  let { basename } = React.useContext(NavigationContext);\n  let location = useLocation();\n  let matches = useMatches();\n  let navigation = useNavigation();\n\n  // Trigger manual scroll restoration while we're active\n  React.useEffect(() => {\n    window.history.scrollRestoration = \"manual\";\n    return () => {\n      window.history.scrollRestoration = \"auto\";\n    };\n  }, []);\n\n  // Save positions on pagehide\n  usePageHide(\n    React.useCallback(() => {\n      if (navigation.state === \"idle\") {\n        let key = (getKey ? getKey(location, matches) : null) || location.key;\n        savedScrollPositions[key] = window.scrollY;\n      }\n      try {\n        sessionStorage.setItem(\n          storageKey || SCROLL_RESTORATION_STORAGE_KEY,\n          JSON.stringify(savedScrollPositions)\n        );\n      } catch (error) {\n        warning(\n          false,\n          `Failed to save scroll positions in sessionStorage, <ScrollRestoration /> will not work properly (${error}).`\n        );\n      }\n      window.history.scrollRestoration = \"auto\";\n    }, [storageKey, getKey, navigation.state, location, matches])\n  );\n\n  // Read in any saved scroll locations\n  if (typeof document !== \"undefined\") {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useLayoutEffect(() => {\n      try {\n        let sessionPositions = sessionStorage.getItem(\n          storageKey || SCROLL_RESTORATION_STORAGE_KEY\n        );\n        if (sessionPositions) {\n          savedScrollPositions = JSON.parse(sessionPositions);\n        }\n      } catch (e) {\n        // no-op, use default empty object\n      }\n    }, [storageKey]);\n\n    // Enable scroll restoration in the router\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useLayoutEffect(() => {\n      let getKeyWithoutBasename: GetScrollRestorationKeyFunction | undefined =\n        getKey && basename !== \"/\"\n          ? (location, matches) =>\n              getKey(\n                // Strip the basename to match useLocation()\n                {\n                  ...location,\n                  pathname:\n                    stripBasename(location.pathname, basename) ||\n                    location.pathname,\n                },\n                matches\n              )\n          : getKey;\n      let disableScrollRestoration = router?.enableScrollRestoration(\n        savedScrollPositions,\n        () => window.scrollY,\n        getKeyWithoutBasename\n      );\n      return () => disableScrollRestoration && disableScrollRestoration();\n    }, [router, basename, getKey]);\n\n    // Restore scrolling when state.restoreScrollPosition changes\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useLayoutEffect(() => {\n      // Explicit false means don't do anything (used for submissions)\n      if (restoreScrollPosition === false) {\n        return;\n      }\n\n      // been here before, scroll to it\n      if (typeof restoreScrollPosition === \"number\") {\n        window.scrollTo(0, restoreScrollPosition);\n        return;\n      }\n\n      // try to scroll to the hash\n      if (location.hash) {\n        let el = document.getElementById(\n          decodeURIComponent(location.hash.slice(1))\n        );\n        if (el) {\n          el.scrollIntoView();\n          return;\n        }\n      }\n\n      // Don't reset if this navigation opted out\n      if (preventScrollReset === true) {\n        return;\n      }\n\n      // otherwise go to the top on new locations\n      window.scrollTo(0, 0);\n    }, [location, restoreScrollPosition, preventScrollReset]);\n  }\n}\n\nexport { useScrollRestoration as UNSAFE_useScrollRestoration };\n\n/**\n * Setup a callback to be fired on the window's `beforeunload` event. This is\n * useful for saving some data to `window.localStorage` just before the page\n * refreshes.\n *\n * Note: The `callback` argument should be a function created with\n * `React.useCallback()`.\n */\nexport function useBeforeUnload(\n  callback: (event: BeforeUnloadEvent) => any,\n  options?: { capture?: boolean }\n): void {\n  let { capture } = options || {};\n  React.useEffect(() => {\n    let opts = capture != null ? { capture } : undefined;\n    window.addEventListener(\"beforeunload\", callback, opts);\n    return () => {\n      window.removeEventListener(\"beforeunload\", callback, opts);\n    };\n  }, [callback, capture]);\n}\n\n/**\n * Setup a callback to be fired on the window's `pagehide` event. This is\n * useful for saving some data to `window.localStorage` just before the page\n * refreshes.  This event is better supported than beforeunload across browsers.\n *\n * Note: The `callback` argument should be a function created with\n * `React.useCallback()`.\n */\nfunction usePageHide(\n  callback: (event: PageTransitionEvent) => any,\n  options?: { capture?: boolean }\n): void {\n  let { capture } = options || {};\n  React.useEffect(() => {\n    let opts = capture != null ? { capture } : undefined;\n    window.addEventListener(\"pagehide\", callback, opts);\n    return () => {\n      window.removeEventListener(\"pagehide\", callback, opts);\n    };\n  }, [callback, capture]);\n}\n\n/**\n * Wrapper around useBlocker to show a window.confirm prompt to users instead\n * of building a custom UI with useBlocker.\n *\n * Warning: This has *a lot of rough edges* and behaves very differently (and\n * very incorrectly in some cases) across browsers if user click addition\n * back/forward navigations while the confirm is open.  Use at your own risk.\n */\nfunction usePrompt({ when, message }: { when: boolean; message: string }) {\n  let blocker = useBlocker(when);\n\n  React.useEffect(() => {\n    if (blocker.state === \"blocked\") {\n      let proceed = window.confirm(message);\n      if (proceed) {\n        // This timeout is needed to avoid a weird \"race\" on POP navigations\n        // between the `window.history` revert navigation and the result of\n        // `window.confirm`\n        setTimeout(blocker.proceed, 0);\n      } else {\n        blocker.reset();\n      }\n    }\n  }, [blocker, message]);\n\n  React.useEffect(() => {\n    if (blocker.state === \"blocked\" && !when) {\n      blocker.reset();\n    }\n  }, [blocker, when]);\n}\n\nexport { usePrompt as unstable_usePrompt };\n\n/**\n * Return a boolean indicating if there is an active view transition to the\n * given href.  You can use this value to render CSS classes or viewTransitionName\n * styles onto your elements\n *\n * @param href The destination href\n * @param [opts.relative] Relative routing type (\"route\" | \"path\")\n */\nfunction useViewTransitionState(\n  to: To,\n  opts: { relative?: RelativeRoutingType } = {}\n) {\n  let vtContext = React.useContext(ViewTransitionContext);\n\n  invariant(\n    vtContext != null,\n    \"`unstable_useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  \" +\n      \"Did you accidentally import `RouterProvider` from `react-router`?\"\n  );\n\n  let { basename } = useDataRouterContext(\n    DataRouterHook.useViewTransitionState\n  );\n  let path = useResolvedPath(to, { relative: opts.relative });\n  if (!vtContext.isTransitioning) {\n    return false;\n  }\n\n  let currentPath =\n    stripBasename(vtContext.currentLocation.pathname, basename) ||\n    vtContext.currentLocation.pathname;\n  let nextPath =\n    stripBasename(vtContext.nextLocation.pathname, basename) ||\n    vtContext.nextLocation.pathname;\n\n  // Transition is active if we're going to or coming from the indicated\n  // destination.  This ensures that other PUSH navigations that reverse\n  // an indicated transition apply.  I.e., on the list view you have:\n  //\n  //   <NavLink to=\"/details/1\" unstable_viewTransition>\n  //\n  // If you click the breadcrumb back to the list view:\n  //\n  //   <NavLink to=\"/list\" unstable_viewTransition>\n  //\n  // We should apply the transition because it's indicated as active going\n  // from /list -> /details/1 and therefore should be active on the reverse\n  // (even though this isn't strictly a POP reverse)\n  return (\n    matchPath(path.pathname, nextPath) != null ||\n    matchPath(path.pathname, currentPath) != null\n  );\n}\n\nexport { useViewTransitionState as unstable_useViewTransitionState };\n\n//#endregion\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOO,IAAMA,gBAAgC;AAC7C,IAAMC,iBAA8B;AAE9B,SAAUC,cAAcC,QAAW;AACvC,SAAOA,UAAU,QAAQ,OAAOA,OAAOC,YAAY;AACrD;AAEM,SAAUC,gBAAgBF,QAAW;AACzC,SAAOD,cAAcC,MAAM,KAAKA,OAAOC,QAAQE,YAAW,MAAO;AACnE;AAEM,SAAUC,cAAcJ,QAAW;AACvC,SAAOD,cAAcC,MAAM,KAAKA,OAAOC,QAAQE,YAAW,MAAO;AACnE;AAEM,SAAUE,eAAeL,QAAW;AACxC,SAAOD,cAAcC,MAAM,KAAKA,OAAOC,QAAQE,YAAW,MAAO;AACnE;AAOA,SAASG,gBAAgBC,OAAwB;AAC/C,SAAO,CAAC,EAAEA,MAAMC,WAAWD,MAAME,UAAUF,MAAMG,WAAWH,MAAMI;AACpE;AAEgB,SAAAC,uBACdL,OACAM,QAAe;AAEf,SACEN,MAAMO,WAAW;GAChB,CAACD,UAAUA,WAAW;EACvB,CAACP,gBAAgBC,KAAK;AAE1B;AA+BgB,SAAAQ,mBACdC,MAA8B;AAAA,MAA9BA,SAAA,QAAA;AAAAA,WAA4B;EAAE;AAE9B,SAAO,IAAIC,gBACT,OAAOD,SAAS,YAChBE,MAAMC,QAAQH,IAAI,KAClBA,gBAAgBC,kBACZD,OACAI,OAAOC,KAAKL,IAAI,EAAEM,OAAO,CAACC,MAAMC,QAAO;AACrC,QAAIC,QAAQT,KAAKQ,GAAG;AACpB,WAAOD,KAAKG,OACVR,MAAMC,QAAQM,KAAK,IAAIA,MAAME,IAAKC,OAAM,CAACJ,KAAKI,CAAC,CAAC,IAAI,CAAC,CAACJ,KAAKC,KAAK,CAAC,CAAC;KAEnE,CAAA,CAAyB,CAAC;AAErC;AAEgB,SAAAI,2BACdC,gBACAC,qBAA2C;AAE3C,MAAIC,eAAejB,mBAAmBe,cAAc;AAEpD,MAAIC,qBAAqB;AAMvBA,wBAAoBE,QAAQ,CAACC,GAAGV,QAAO;AACrC,UAAI,CAACQ,aAAaG,IAAIX,GAAG,GAAG;AAC1BO,4BAAoBK,OAAOZ,GAAG,EAAES,QAASR,WAAS;AAChDO,uBAAaK,OAAOb,KAAKC,KAAK;QAChC,CAAC;MACF;IACH,CAAC;EACF;AAED,SAAOO;AACT;AAoBA,IAAIM,6BAA6C;AAEjD,SAASC,+BAA4B;AACnC,MAAID,+BAA+B,MAAM;AACvC,QAAI;AACF,UAAIE;QACFC,SAASC,cAAc,MAAM;;QAE7B;MAAC;AAEHJ,mCAA6B;aACtBK,GAAG;AACVL,mCAA6B;IAC9B;EACF;AACD,SAAOA;AACT;AAoDA,IAAMM,wBAA0C,oBAAIC,IAAI,CACtD,qCACA,uBACA,YAAY,CACb;AAED,SAASC,eAAeC,SAAsB;AAC5C,MAAIA,WAAW,QAAQ,CAACH,sBAAsBT,IAAIY,OAAsB,GAAG;AACzEC,WAAAC,QACE,OACA,MAAIF,UACsBjD,+DAAAA,0BAAAA,iBAAc,IAAG,IAC5C;AAED,WAAO;EACR;AACD,SAAOiD;AACT;AAEgB,SAAAG,sBACdrC,QACAsC,UAAgB;AAQhB,MAAIC;AACJ,MAAIC;AACJ,MAAIN;AACJ,MAAIO;AACJ,MAAIC;AAEJ,MAAInD,cAAcS,MAAM,GAAG;AAIzB,QAAI2C,OAAO3C,OAAO4C,aAAa,QAAQ;AACvCJ,aAASG,OAAOE,cAAcF,MAAML,QAAQ,IAAI;AAChDC,aAASvC,OAAO4C,aAAa,QAAQ,KAAK5D;AAC1CkD,cAAUD,eAAejC,OAAO4C,aAAa,SAAS,CAAC,KAAK3D;AAE5DwD,eAAW,IAAId,SAAS3B,MAAM;aAE9BX,gBAAgBW,MAAM,KACrBR,eAAeQ,MAAM,MACnBA,OAAO8C,SAAS,YAAY9C,OAAO8C,SAAS,UAC/C;AACA,QAAIC,OAAO/C,OAAO+C;AAElB,QAAIA,QAAQ,MAAM;AAChB,YAAM,IAAIC,MAAK,oEACuD;IAEvE;AAOD,QAAIL,OAAO3C,OAAO4C,aAAa,YAAY,KAAKG,KAAKH,aAAa,QAAQ;AAC1EJ,aAASG,OAAOE,cAAcF,MAAML,QAAQ,IAAI;AAEhDC,aACEvC,OAAO4C,aAAa,YAAY,KAChCG,KAAKH,aAAa,QAAQ,KAC1B5D;AACFkD,cACED,eAAejC,OAAO4C,aAAa,aAAa,CAAC,KACjDX,eAAec,KAAKH,aAAa,SAAS,CAAC,KAC3C3D;AAGFwD,eAAW,IAAId,SAASoB,MAAM/C,MAAM;AAMpC,QAAI,CAAC0B,6BAA4B,GAAI;AACnC,UAAI;QAAEuB;QAAMH;QAAMlC;MAAK,IAAKZ;AAC5B,UAAI8C,SAAS,SAAS;AACpB,YAAII,SAASD,OAAUA,OAAI,MAAM;AACjCR,iBAASjB,OAAU0B,SAAM,KAAK,GAAG;AACjCT,iBAASjB,OAAU0B,SAAM,KAAK,GAAG;iBACxBD,MAAM;AACfR,iBAASjB,OAAOyB,MAAMrC,KAAK;MAC5B;IACF;EACF,WAAU1B,cAAcc,MAAM,GAAG;AAChC,UAAM,IAAIgD,MACR,oFAC+B;EAElC,OAAM;AACLT,aAASvD;AACTwD,aAAS;AACTN,cAAUjD;AACVyD,WAAO1C;EACR;AAGD,MAAIyC,YAAYP,YAAY,cAAc;AACxCQ,WAAOD;AACPA,eAAWU;EACZ;AAED,SAAO;IAAEX;IAAQD,QAAQA,OAAOjD,YAAW;IAAI4C;IAASO;IAAUC;;AACpE;;;;AClFgB,SAAAU,oBACdC,QACAC,MAAoB;AAEpB,SAAOC,aAAa;IAClBjB,UAAUgB,QAAAA,OAAAA,SAAAA,KAAMhB;IAChBkB,QAAMC,SAAA,CAAA,GACDH,QAAAA,OAAAA,SAAAA,KAAME,QAAM;MACfE,oBAAoB;KACrB;IACDC,SAASC,qBAAqB;MAAEC,QAAQP,QAAAA,OAAAA,SAAAA,KAAMO;IAAM,CAAE;IACtDC,gBAAeR,QAAAA,OAAAA,SAAAA,KAAMQ,kBAAiBC,mBAAkB;IACxDV;;IAEAQ,QAAQP,QAAAA,OAAAA,SAAAA,KAAMO;GACf,EAAEG,WAAU;AACf;AAEgB,SAAAC,iBACdZ,QACAC,MAAoB;AAEpB,SAAOC,aAAa;IAClBjB,UAAUgB,QAAAA,OAAAA,SAAAA,KAAMhB;IAChBkB,QAAMC,SAAA,CAAA,GACDH,QAAAA,OAAAA,SAAAA,KAAME,QAAM;MACfE,oBAAoB;KACrB;IACDC,SAASO,kBAAkB;MAAEL,QAAQP,QAAAA,OAAAA,SAAAA,KAAMO;IAAM,CAAE;IACnDC,gBAAeR,QAAAA,OAAAA,SAAAA,KAAMQ,kBAAiBC,mBAAkB;IACxDV;;IAEAQ,QAAQP,QAAAA,OAAAA,SAAAA,KAAMO;GACf,EAAEG,WAAU;AACf;AAEA,SAASD,qBAAkB;AAAA,MAAAI;AACzB,MAAIC,SAAKD,UAAGN,WAAAM,OAAAA,SAAAA,QAAQE;AACpB,MAAID,SAASA,MAAME,QAAQ;AACzBF,YAAKX,SAAA,CAAA,GACAW,OAAK;MACRE,QAAQC,kBAAkBH,MAAME,MAAM;KACvC;EACF;AACD,SAAOF;AACT;AAEA,SAASG,kBACPD,QAAsC;AAEtC,MAAI,CAACA,OAAQ,QAAO;AACpB,MAAIE,UAAUjE,OAAOiE,QAAQF,MAAM;AACnC,MAAIG,aAA6C,CAAA;AACjD,WAAS,CAAC9D,KAAK+D,GAAG,KAAKF,SAAS;AAG9B,QAAIE,OAAOA,IAAIC,WAAW,sBAAsB;AAC9CF,iBAAW9D,GAAG,IAAI,IAAIiE,kBACpBF,IAAIG,QACJH,IAAII,YACJJ,IAAIK,MACJL,IAAIM,aAAa,IAAI;eAEdN,OAAOA,IAAIC,WAAW,SAAS;AAExC,UAAID,IAAIO,WAAW;AACjB,YAAIC,mBAAmBrB,OAAOa,IAAIO,SAAS;AAC3C,YAAI,OAAOC,qBAAqB,YAAY;AAC1C,cAAI;AAEF,gBAAIC,QAAQ,IAAID,iBAAiBR,IAAIU,OAAO;AAG5CD,kBAAME,QAAQ;AACdZ,uBAAW9D,GAAG,IAAIwE;mBACXrD,GAAG;UACV;QAEH;MACF;AAED,UAAI2C,WAAW9D,GAAG,KAAK,MAAM;AAC3B,YAAIwE,QAAQ,IAAInC,MAAM0B,IAAIU,OAAO;AAGjCD,cAAME,QAAQ;AACdZ,mBAAW9D,GAAG,IAAIwE;MACnB;IACF,OAAM;AACLV,iBAAW9D,GAAG,IAAI+D;IACnB;EACF;AACD,SAAOD;AACT;AAkBA,IAAMa,wBAA8BC,oBAA2C;EAC7EC,iBAAiB;AAClB,CAAA;AACD,IAAArD,MAAa;AACXmD,wBAAsBG,cAAc;AACrC;AA+BD,IAAMC,mBAAmB;AACzB,IAAMC,sBAAsBC,MAAMF,gBAAgB;AAElD,SAASG,oBAAoBC,IAAc;AACzC,MAAIH,qBAAqB;AACvBA,wBAAoBG,EAAE;EACvB,OAAM;AACLA,OAAE;EACH;AACH;AASA,IAAMC,WAAN,MAAc;EAOZC,cAAA;AANA,SAAMnB,SAAwC;AAO5C,SAAKoB,UAAU,IAAIC,QAAQ,CAACC,SAASC,WAAU;AAC7C,WAAKD,UAAWvF,WAAS;AACvB,YAAI,KAAKiE,WAAW,WAAW;AAC7B,eAAKA,SAAS;AACdsB,kBAAQvF,KAAK;QACd;;AAEH,WAAKwF,SAAUC,YAAU;AACvB,YAAI,KAAKxB,WAAW,WAAW;AAC7B,eAAKA,SAAS;AACduB,iBAAOC,MAAM;QACd;;IAEL,CAAC;EACH;AACD;AAKK,SAAUC,eAAcC,MAIR;AAAA,MAJS;IAC7BC;IACAC;IACAjD;EACoB,IAAA+C;AACpB,MAAI,CAACnC,OAAOsC,YAAY,IAAUC,eAASF,OAAOrC,KAAK;AACvD,MAAI,CAACwC,cAAcC,eAAe,IAAUF,eAAQ;AACpD,MAAI,CAACG,WAAWC,YAAY,IAAUJ,eAAsC;IAC1EnB,iBAAiB;EAClB,CAAA;AACD,MAAI,CAACwB,WAAWC,YAAY,IAAUN,eAAQ;AAC9C,MAAI,CAACO,YAAYC,aAAa,IAAUR,eAAQ;AAChD,MAAI,CAACS,cAAcC,eAAe,IAAUV,eAAQ;AAKpD,MAAI;IAAEW;EAAkB,IAAK9D,UAAU,CAAA;AAEvC,MAAI+D,uBAA6BC,kBAC9B1B,QAAkB;AACjB,QAAIwB,oBAAoB;AACtBzB,0BAAoBC,EAAE;IACvB,OAAM;AACLA,SAAE;IACH;EACH,GACA,CAACwB,kBAAkB,CAAC;AAGtB,MAAIG,WAAiBD,kBACnB,CACEE,UAAqBC,UAEnB;AAAA,QADF;MAAEC,6BAA6BC;IAAkB,IAAEF;AAEnD,QACE,CAACE,sBACDpB,OAAO5C,UAAU,QACjB,OAAO4C,OAAO5C,OAAOjC,SAASkG,wBAAwB,YACtD;AAEAP,2BAAqB,MAAMb,aAAagB,QAAQ,CAAC;IAClD,WAAUR,cAAcF,WAAW;AAGlCA,gBAAUb,QAAO;AACjBe,iBAAWa,eAAc;AACzBV,sBAAgB;QACdjD,OAAOsD;QACPM,iBAAiBH,mBAAmBG;QACpCC,cAAcJ,mBAAmBI;MAClC,CAAA;IACF,OAAM;AAELpB,sBAAgBa,QAAQ;AACxBX,mBAAa;QACXvB,iBAAiB;QACjBwC,iBAAiBH,mBAAmBG;QACpCC,cAAcJ,mBAAmBI;MAClC,CAAA;IACF;EACH,GACA,CAACV,sBAAsBL,YAAYF,WAAWP,OAAO5C,MAAM,CAAC;AAK9D+B,EAAMsC,sBAAgB,MAAMzB,OAAO0B,UAAUV,QAAQ,GAAG,CAAChB,QAAQgB,QAAQ,CAAC;AAI1E7B,EAAMwC,gBAAU,MAAK;AACnB,QAAItB,UAAUtB,iBAAiB;AAC7ByB,mBAAa,IAAIlB,SAAQ,CAAQ;IAClC;EACH,GAAG,CAACe,UAAUtB,eAAe,CAAC;AAK9BI,EAAMwC,gBAAU,MAAK;AACnB,QAAIpB,aAAaJ,gBAAgBH,OAAO5C,QAAQ;AAC9C,UAAI6D,WAAWd;AACf,UAAIyB,gBAAgBrB,UAAUf;AAC9B,UAAIiB,cAAaT,OAAO5C,OAAOjC,SAASkG,oBAAoB,YAAW;AACrEP,6BAAqB,MAAMb,aAAagB,QAAQ,CAAC;AACjD,cAAMW;MACR,CAAC;AACDnB,MAAAA,YAAWoB,SAASC,QAAQ,MAAK;AAC/BtB,qBAAa9D,MAAS;AACtBgE,sBAAchE,MAAS;AACvB0D,wBAAgB1D,MAAS;AACzB4D,qBAAa;UAAEvB,iBAAiB;QAAK,CAAE;MACzC,CAAC;AACD2B,oBAAcD,WAAU;IACzB;EACH,GAAG,CAACK,sBAAsBX,cAAcI,WAAWP,OAAO5C,MAAM,CAAC;AAIjE+B,EAAMwC,gBAAU,MAAK;AACnB,QACEpB,aACAJ,gBACAxC,MAAMoE,SAAS7H,QAAQiG,aAAa4B,SAAS7H,KAC7C;AACAqG,gBAAUb,QAAO;IAClB;EACH,GAAG,CAACa,WAAWE,YAAY9C,MAAMoE,UAAU5B,YAAY,CAAC;AAIxDhB,EAAMwC,gBAAU,MAAK;AACnB,QAAI,CAACtB,UAAUtB,mBAAmB4B,cAAc;AAC9CP,sBAAgBO,aAAahD,KAAK;AAClC2C,mBAAa;QACXvB,iBAAiB;QACjBwC,iBAAiBZ,aAAaY;QAC9BC,cAAcb,aAAaa;MAC5B,CAAA;AACDZ,sBAAgBlE,MAAS;IAC1B;KACA,CAAC2D,UAAUtB,iBAAiB4B,YAAY,CAAC;AAE5C,MAAIqB,YAAkBC,cAAQ,MAAgB;AAC5C,WAAO;MACLC,YAAYlC,OAAOkC;MACnBC,gBAAgBnC,OAAOmC;MACvBC,IAAKC,OAAMrC,OAAOsC,SAASD,CAAC;MAC5BE,MAAMA,CAACC,IAAI7E,QAAOd,SAChBmD,OAAOsC,SAASE,IAAI;QAClB7E,OAAAA;QACA8E,oBAAoB5F,QAAAA,OAAAA,SAAAA,KAAM4F;OAC3B;MACHC,SAASA,CAACF,IAAI7E,QAAOd,SACnBmD,OAAOsC,SAASE,IAAI;QAClBE,SAAS;QACT/E,OAAAA;QACA8E,oBAAoB5F,QAAAA,OAAAA,SAAAA,KAAM4F;OAC3B;;EAEP,GAAG,CAACzC,MAAM,CAAC;AAEX,MAAInE,WAAWmE,OAAOnE,YAAY;AAElC,MAAI8G,oBAA0BV,cAC5B,OAAO;IACLjC;IACAgC;IACAY,QAAQ;IACR/G;MAEF,CAACmE,QAAQgC,WAAWnG,QAAQ,CAAC;AAS/B,SACET,oBAAAyH,gBAAA,MACEzH,oBAAC0H,kBAAkBC,UAAS;IAAA5I,OAAOwI;KACjCvH,oBAAC4H,uBAAuBD,UAAS;IAAA5I,OAAOwD;KACtCvC,oBAACyD,sBAAsBkE,UAAS;IAAA5I,OAAOkG;EAAS,GAC7CjF,oBAAA6H,QACC;IAAApH;IACAkG,UAAUpE,MAAMoE;IAChBmB,gBAAgBvF,MAAMwF;IACtBnB;KAECrE,MAAMyF,cACJhI,oBAAAiI,YAAW;IAAAzG,QAAQoD,OAAOpD;IAAQe;GAAgB,IAEnDoC,eACD,CACM,CACsB,CACD,GAEnC,IAAI;AAGX;AAEA,SAASsD,WAAUC,OAMlB;AAAA,MANmB;IAClB1G;IACAe;EAID,IAAA2F;AACC,SAAOC,cAAc3G,QAAQF,QAAWiB,KAAK;AAC/C;AAYM,SAAU6F,cAAaC,OAKR;AAAA,MALS;IAC5B5H;IACA6H;IACA3G;IACAK,QAAAA;EACmB,IAAAqG;AACnB,MAAIE,aAAmBC,aAAM;AAC7B,MAAID,WAAWE,WAAW,MAAM;AAC9BF,eAAWE,UAAU1G,qBAAqB;MAAEC,QAAAA;MAAQ0G,UAAU;IAAI,CAAE;EACrE;AAED,MAAI5G,UAAUyG,WAAWE;AACzB,MAAI,CAAClG,OAAOsC,YAAY,IAAUC,eAAS;IACzCnE,QAAQmB,QAAQnB;IAChBgG,UAAU7E,QAAQ6E;EACnB,CAAA;AACD,MAAI;IAAElB;EAAkB,IAAK9D,UAAU,CAAA;AACvC,MAAIiE,WAAiBD,kBAClBE,cAA4D;AAC3DJ,0BAAsB3B,sBAClBA,oBAAoB,MAAMe,aAAagB,QAAQ,CAAC,IAChDhB,aAAagB,QAAQ;EAC3B,GACA,CAAChB,cAAcY,kBAAkB,CAAC;AAGpC1B,EAAMsC,sBAAgB,MAAMvE,QAAQ6G,OAAO/C,QAAQ,GAAG,CAAC9D,SAAS8D,QAAQ,CAAC;AAEzE,SACE5F,oBAAC6H,QAAM;IACLpH;IACA6H;IACA3B,UAAUpE,MAAMoE;IAChBmB,gBAAgBvF,MAAM5B;IACtBiG,WAAW9E;EAAO,CAAA;AAGxB;AAaM,SAAU8G,WAAUC,OAKR;AAAA,MALS;IACzBpI;IACA6H;IACA3G;IACAK,QAAAA;EACgB,IAAA6G;AAChB,MAAIN,aAAmBC,aAAM;AAC7B,MAAID,WAAWE,WAAW,MAAM;AAC9BF,eAAWE,UAAUpG,kBAAkB;MAAEL,QAAAA;MAAQ0G,UAAU;IAAI,CAAE;EAClE;AAED,MAAI5G,UAAUyG,WAAWE;AACzB,MAAI,CAAClG,OAAOsC,YAAY,IAAUC,eAAS;IACzCnE,QAAQmB,QAAQnB;IAChBgG,UAAU7E,QAAQ6E;EACnB,CAAA;AACD,MAAI;IAAElB;EAAkB,IAAK9D,UAAU,CAAA;AACvC,MAAIiE,WAAiBD,kBAClBE,cAA4D;AAC3DJ,0BAAsB3B,sBAClBA,oBAAoB,MAAMe,aAAagB,QAAQ,CAAC,IAChDhB,aAAagB,QAAQ;EAC3B,GACA,CAAChB,cAAcY,kBAAkB,CAAC;AAGpC1B,EAAMsC,sBAAgB,MAAMvE,QAAQ6G,OAAO/C,QAAQ,GAAG,CAAC9D,SAAS8D,QAAQ,CAAC;AAEzE,SACE5F,oBAAC6H,QAAM;IACLpH;IACA6H;IACA3B,UAAUpE,MAAMoE;IAChBmB,gBAAgBvF,MAAM5B;IACtBiG,WAAW9E;EAAO,CAAA;AAGxB;AAeA,SAASgH,cAAaC,OAKD;AAAA,MALE;IACrBtI;IACA6H;IACA3G;IACAG;EACmB,IAAAiH;AACnB,MAAI,CAACxG,OAAOsC,YAAY,IAAUC,eAAS;IACzCnE,QAAQmB,QAAQnB;IAChBgG,UAAU7E,QAAQ6E;EACnB,CAAA;AACD,MAAI;IAAElB;EAAkB,IAAK9D,UAAU,CAAA;AACvC,MAAIiE,WAAiBD,kBAClBE,cAA4D;AAC3DJ,0BAAsB3B,sBAClBA,oBAAoB,MAAMe,aAAagB,QAAQ,CAAC,IAChDhB,aAAagB,QAAQ;EAC3B,GACA,CAAChB,cAAcY,kBAAkB,CAAC;AAGpC1B,EAAMsC,sBAAgB,MAAMvE,QAAQ6G,OAAO/C,QAAQ,GAAG,CAAC9D,SAAS8D,QAAQ,CAAC;AAEzE,SACE5F,oBAAC6H,QAAM;IACLpH;IACA6H;IACA3B,UAAUpE,MAAMoE;IAChBmB,gBAAgBvF,MAAM5B;IACtBiG,WAAW9E;EAAO,CAAA;AAGxB;AAEA,IAAAxB,MAAa;AACXwI,gBAAclF,cAAc;AAC7B;AAeD,IAAMoF,YACJ,OAAOhH,WAAW,eAClB,OAAOA,OAAOjC,aAAa,eAC3B,OAAOiC,OAAOjC,SAASC,kBAAkB;AAE3C,IAAMiJ,qBAAqB;AAKdC,IAAAA,OAAaC,iBACxB,SAASC,YAAWC,OAalBC,KAAG;AAAA,MAZH;IACEC;IACAC;IACAC;IACAnC;IACA/E;IACApE;IACAiJ;IACAC;IACAqC;EACO,IACRL,OADIM,OAAIC,8BAAAP,OAAAQ,SAAA;AAIT,MAAI;IAAEpJ;EAAQ,IAAWqJ,iBAAWC,iBAAiB;AAGrD,MAAIC;AACJ,MAAIC,aAAa;AAEjB,MAAI,OAAO7C,OAAO,YAAY6B,mBAAmBiB,KAAK9C,EAAE,GAAG;AAEzD4C,mBAAe5C;AAGf,QAAI4B,WAAW;AACb,UAAI;AACF,YAAImB,aAAa,IAAIC,IAAIpI,OAAO2E,SAAS0D,IAAI;AAC7C,YAAIC,YAAYlD,GAAGmD,WAAW,IAAI,IAC9B,IAAIH,IAAID,WAAWK,WAAWpD,EAAE,IAChC,IAAIgD,IAAIhD,EAAE;AACd,YAAIqD,OAAOzJ,cAAcsJ,UAAUI,UAAUjK,QAAQ;AAErD,YAAI6J,UAAUK,WAAWR,WAAWQ,UAAUF,QAAQ,MAAM;AAE1DrD,eAAKqD,OAAOH,UAAUM,SAASN,UAAUO;QAC1C,OAAM;AACLZ,uBAAa;QACd;eACMhK,GAAG;AAEVK,eAAAC,QACE,OACA,eAAa6G,KAAE,wGACsC,IACtD;MACF;IACF;EACF;AAGD,MAAIiD,OAAOS,QAAQ1D,IAAI;IAAEoC;EAAU,CAAA;AAEnC,MAAIuB,kBAAkBC,oBAAoB5D,IAAI;IAC5CE;IACA/E;IACApE;IACAkJ;IACAmC;IACAE;EACD,CAAA;AACD,WAASuB,YACPpN,OAAsD;AAEtD,QAAI0L,QAASA,SAAQ1L,KAAK;AAC1B,QAAI,CAACA,MAAMqN,kBAAkB;AAC3BH,sBAAgBlN,KAAK;IACtB;EACH;AAEA;;IAEEmC,oBAAA,KAAA4B,SAAA,CAAA,GACM+H,MAAI;MACRU,MAAML,gBAAgBK;MACtBd,SAASU,cAAcR,iBAAiBF,UAAU0B;MAClD3B;MACAnL;KAAc,CAAA;;AAGpB,CAAC;AAGH,IAAAmC,MAAa;AACX4I,OAAKtF,cAAc;AACpB;AAuBYuH,IAAAA,UAAgBhC,iBAC3B,SAASiC,eAAcC,OAYrB/B,KAAG;AAAA,MAXH;IACE,gBAAgBgC,kBAAkB;IAClCC,gBAAgB;IAChBC,WAAWC,gBAAgB;IAC3BC,MAAM;IACNC,OAAOC;IACPxE;IACAsC;IACApB;EAED,IAAA+C,OADI1B,OAAIC,8BAAAyB,OAAAQ,UAAA;AAIT,MAAIpB,OAAOqB,gBAAgB1E,IAAI;IAAEoC,UAAUG,KAAKH;EAAQ,CAAE;AAC1D,MAAI7C,WAAWoF,YAAW;AAC1B,MAAIC,cAAoBlC,iBAAWlC,sBAAsB;AACzD,MAAI;IAAEhB;EAAS,IAAWkD,iBAAWC,iBAAiB;AACtD,MAAIpG,kBACFqI,eAAe;;EAGfC,uBAAuBxB,IAAI,KAC3Bf,4BAA4B;AAE9B,MAAIwC,aAAatF,UAAUG,iBACvBH,UAAUG,eAAe0D,IAAI,EAAEC,WAC/BD,KAAKC;AACT,MAAIyB,mBAAmBxF,SAAS+D;AAChC,MAAI0B,uBACFJ,eAAeA,YAAYK,cAAcL,YAAYK,WAAW1F,WAC5DqF,YAAYK,WAAW1F,SAAS+D,WAChC;AAEN,MAAI,CAACa,eAAe;AAClBY,uBAAmBA,iBAAiB1O,YAAW;AAC/C2O,2BAAuBA,uBACnBA,qBAAqB3O,YAAW,IAChC;AACJyO,iBAAaA,WAAWzO,YAAW;EACpC;AAED,MAAI6O,WACFH,qBAAqBD,cACpB,CAACR,OACAS,iBAAiB5B,WAAW2B,UAAU,KACtCC,iBAAiBI,OAAOL,WAAWM,MAAM,MAAM;AAEnD,MAAIC,YACFL,wBAAwB,SACvBA,yBAAyBF,cACvB,CAACR,OACAU,qBAAqB7B,WAAW2B,UAAU,KAC1CE,qBAAqBG,OAAOL,WAAWM,MAAM,MAAM;AAEzD,MAAIE,cAAc;IAChBJ;IACAG;IACA9I;;AAGF,MAAIgJ,cAAcL,WAAWhB,kBAAkBhK;AAE/C,MAAIkK;AACJ,MAAI,OAAOC,kBAAkB,YAAY;AACvCD,gBAAYC,cAAciB,WAAW;EACtC,OAAM;AAMLlB,gBAAY,CACVC,eACAa,WAAW,WAAW,MACtBG,YAAY,YAAY,MACxB9I,kBAAkB,kBAAkB,IAAI,EAEvCiJ,OAAOC,OAAO,EACdC,KAAK,GAAG;EACZ;AAED,MAAInB,QACF,OAAOC,cAAc,aAAaA,UAAUc,WAAW,IAAId;AAE7D,SACE7H,oBAACmF,MAAItH,SAAA,CAAA,GACC+H,MAAI;IACM,gBAAAgD;IACdnB;IACAlC;IACAqC;IACAvE;IACAsC;GAEC,GAAA,OAAOpB,aAAa,aAAaA,SAASoE,WAAW,IAAIpE,QAAQ;AAGxE,CAAC;AAGH,IAAAhI,MAAa;AACX6K,UAAQvH,cAAc;AACvB;AA0EM,IAAMmJ,OAAa5D,iBACxB,CAAC6D,OAAO1D,QAAO;AACb,MAAI2D,SAASC,UAAS;AACtB,SAAQlN,oBAAAmN,UAAQvL,SAAA,CAAA,GAAKoL,OAAK;IAAEC;IAAgB3D;EAAQ,CAAA,CAAA;AACtD,CAAC;AAGH,IAAAhJ,MAAa;AACXyM,OAAKnJ,cAAc;AACpB;AAcD,IAAMuJ,WAAiBhE,iBACrB,CAAAiE,OAcEC,iBACE;AAAA,MAdF;IACE5D;IACAnC;IACA/E;IACA7B,SAASvD;IACTwD;IACA2M;IACAL;IACAzD;IACAnC;IACAqC;EACQ,IACT0D,OADIJ,QAAKpD,8BAAAwD,OAAAG,UAAA;AAIV,MAAIC,aACF9M,OAAOjD,YAAW,MAAO,QAAQ,QAAQ;AAC3C,MAAIgQ,aAAaC,cAAc/M,QAAQ;IAAE6I;EAAU,CAAA;AACnD,MAAImE,gBAA0D9P,WAAS;AACrEyP,gBAAYA,SAASzP,KAAK;AAC1B,QAAIA,MAAMqN,iBAAkB;AAC5BrN,UAAM+P,eAAc;AAEpB,QAAIC,YAAahQ,MAAqCiQ,YACnDD;AAEH,QAAIE,gBACDF,aAAAA,OAAAA,SAAAA,UAAW9M,aAAa,YAAY,MACrCL;AAEFuM,WAAOY,aAAahQ,MAAMmQ,eAAe;MACvCtN,QAAQqN;MACRzG;MACA/E;MACAiH;MACAnC;MACAqC;IACD,CAAA;;AAGH,SACE1J,oBAAA,QAAA4B,SAAA;IACE0H,KAAK+D;IACL3M,QAAQ8M;IACR7M,QAAQ8M;IACRH,UAAU7D,iBAAiB6D,WAAWK;KAClCX,KAAK,CAAA;AAGf,CAAC;AAGH,IAAA1M,MAAa;AACX6M,WAASvJ,cAAc;AACxB;SAWeqK,kBAAiBC,QAGR;AAAA,MAHS;IAChCC;IACAC;EACuB,IAAAF;AACvBG,uBAAqB;IAAEF;IAAQC;EAAU,CAAE;AAC3C,SAAO;AACT;AAEA,IAAA9N,MAAa;AACX2N,oBAAkBrK,cAAc;AACjC;AAOD,IAAK0K;CAAL,SAAKA,iBAAc;AACjBA,EAAAA,gBAAA,sBAAA,IAAA;AACAA,EAAAA,gBAAA,WAAA,IAAA;AACAA,EAAAA,gBAAA,kBAAA,IAAA;AACAA,EAAAA,gBAAA,YAAA,IAAA;AACAA,EAAAA,gBAAA,wBAAA,IAAA;AACF,GANKA,mBAAAA,iBAMJ,CAAA,EAAA;AAED,IAAKC;CAAL,SAAKA,sBAAmB;AACtBA,EAAAA,qBAAA,aAAA,IAAA;AACAA,EAAAA,qBAAA,sBAAA,IAAA;AACF,GAHKA,wBAAAA,sBAGJ,CAAA,EAAA;AAED,SAASC,0BACPC,UAA8C;AAE9C,SAAUA,WAAQ;AACpB;AAEA,SAASC,qBAAqBD,UAAwB;AACpD,MAAIE,MAAY7E,iBAAWpC,iBAAiB;AAC5C,GAAUiH,MAAGrO,OAAbsO,UAAS,OAAMJ,0BAA0BC,QAAQ,CAAC,IAAlDG,UAAS,KAAA,IAAA;AACT,SAAOD;AACT;AAEA,SAASE,mBAAmBJ,UAA6B;AACvD,MAAIlM,QAAcuH,iBAAWlC,sBAAsB;AACnD,GAAUrF,QAAKjC,OAAfsO,UAAS,OAAQJ,0BAA0BC,QAAQ,CAAC,IAApDG,UAAS,KAAA,IAAA;AACT,SAAOrM;AACT;AAOM,SAAUyI,oBACd5D,IAAM0H,OAeA;AAAA,MAdN;IACE3Q;IACAmJ,SAASyH;IACTxM;IACA8E;IACAmC;IACAE;yBAQE,CAAA,IAAEoF;AAEN,MAAI5H,WAAW8H,YAAW;AAC1B,MAAIrI,WAAWoF,YAAW;AAC1B,MAAItB,OAAOqB,gBAAgB1E,IAAI;IAAEoC;EAAU,CAAA;AAE3C,SAAa7D,kBACV9H,WAA0C;AACzC,QAAIK,uBAAuBL,OAAOM,MAAM,GAAG;AACzCN,YAAM+P,eAAc;AAIpB,UAAItG,UACFyH,gBAAgBzN,SACZyN,cACAE,WAAWtI,QAAQ,MAAMsI,WAAWxE,IAAI;AAE9CvD,eAASE,IAAI;QACXE;QACA/E;QACA8E;QACAmC;QACAE;MACD,CAAA;IACF;KAEH,CACE/C,UACAO,UACAuD,MACAsE,aACAxM,OACApE,QACAiJ,IACAC,oBACAmC,UACAE,uBAAuB,CACxB;AAEL;AAMM,SAAUwF,gBACdC,aAAiC;AAEjC7O,SAAAC,QACE,OAAOhC,oBAAoB,aAC3B,gcAOS,IACV;AAED,MAAI6Q,yBAA+B5G,aAAOnK,mBAAmB8Q,WAAW,CAAC;AACzE,MAAIE,wBAA8B7G,aAAO,KAAK;AAE9C,MAAI7B,WAAWoF,YAAW;AAC1B,MAAIzM,eAAqBuH,cACvB;;;;IAIE1H,2BACEwH,SAASiE,QACTyE,sBAAsB5G,UAAU,OAAO2G,uBAAuB3G,OAAO;KAEzE,CAAC9B,SAASiE,MAAM,CAAC;AAGnB,MAAI1D,WAAW8H,YAAW;AAC1B,MAAIM,kBAAwB3J,kBAC1B,CAAC4J,UAAUC,oBAAmB;AAC5B,UAAMC,kBAAkBpR,mBACtB,OAAOkR,aAAa,aAAaA,SAASjQ,YAAY,IAAIiQ,QAAQ;AAEpEF,0BAAsB5G,UAAU;AAChCvB,aAAS,MAAMuI,iBAAiBD,eAAe;EACjD,GACA,CAACtI,UAAU5H,YAAY,CAAC;AAG1B,SAAO,CAACA,cAAcgQ,eAAe;AACvC;AA2CA,SAASI,+BAA4B;AACnC,MAAI,OAAO3P,aAAa,aAAa;AACnC,UAAM,IAAIoB,MACR,+GACgE;EAEnE;AACH;SAMgB+L,YAAS;AACvB,MAAI;IAAEtI;EAAM,IAAK8J,qBAAqBJ,eAAeqB,SAAS;AAC9D,MAAI;IAAElP;EAAQ,IAAWqJ,iBAAWC,iBAAiB;AACrD,MAAI6F,iBAAiBC,WAAU;AAE/B,SAAalK,kBACX,SAACxH,QAAQ2R,SAAgB;AAAA,QAAhBA,YAAO,QAAA;AAAPA,gBAAU,CAAA;IAAE;AACnBJ,iCAA4B;AAE5B,QAAI;MAAE/O;MAAQD;MAAQL;MAASO;MAAUC;IAAI,IAAKL,sBAChDrC,QACAsC,QAAQ;AAGVmE,WAAOsC,SAAS4I,QAAQnP,UAAUA,QAAQ;MACxC0G,oBAAoByI,QAAQzI;MAC5BzG;MACAC;MACA2M,YAAYsC,QAAQpP,UAAWA;MAC/BqP,aAAaD,QAAQzP,WAAYA;MACjCiH,SAASwI,QAAQxI;MACjB/E,OAAOuN,QAAQvN;MACfyN,aAAaJ;MACblG,yBAAyBoG,QAAQpG;IAClC,CAAA;KAEH,CAAC9E,QAAQnE,UAAUmP,cAAc,CAAC;AAEtC;AAKA,SAASK,iBACPC,YACAC,gBAAsB;AAEtB,MAAI;IAAEvL;EAAM,IAAK8J,qBAAqBJ,eAAe8B,gBAAgB;AACrE,MAAI;IAAE3P;EAAQ,IAAWqJ,iBAAWC,iBAAiB;AAErD,SAAapE,kBACX,SAACxH,QAAQ2R,SAAgB;AAAA,QAAhBA,YAAO,QAAA;AAAPA,gBAAU,CAAA;IAAE;AACnBJ,iCAA4B;AAE5B,QAAI;MAAE/O;MAAQD;MAAQL;MAASO;MAAUC;IAAI,IAAKL,sBAChDrC,QACAsC,QAAQ;AAGV,MACE0P,kBAAkB,QAAI7P,OADxBsO,UAEE,OAAA,uCAAuC,IAFzCA,UAAS,KAAA,IAAA;AAIThK,WAAOyL,MAAMH,YAAYC,gBAAgBL,QAAQnP,UAAUA,QAAQ;MACjE0G,oBAAoByI,QAAQzI;MAC5BzG;MACAC;MACA2M,YAAYsC,QAAQpP,UAAWA;MAC/BqP,aAAaD,QAAQzP,WAAYA;IAClC,CAAA;KAEH,CAACuE,QAAQnE,UAAUyP,YAAYC,cAAc,CAAC;AAElD;AAIM,SAAUzC,cACd/M,QAAe2P,QACsC;AAAA,MAArD;IAAE9G;0BAAiD,CAAA,IAAE8G;AAErD,MAAI;IAAE7P;EAAQ,IAAWqJ,iBAAWC,iBAAiB;AACrD,MAAIwG,eAAqBzG,iBAAW0G,YAAY;AAChD,GAAUD,eAAYjQ,OAAtBsO,UAAS,OAAe,kDAAkD,IAA1EA,UAAS,KAAA,IAAA;AAET,MAAI,CAAC6B,KAAK,IAAIF,aAAaG,QAAQC,MAAM,EAAE;AAG3C,MAAIlG,OAAI7I,SAAQkK,CAAAA,GAAAA,gBAAgBnL,SAASA,SAAS,KAAK;IAAE6I;EAAQ,CAAE,CAAC;AAOpE,MAAI7C,WAAWoF,YAAW;AAC1B,MAAIpL,UAAU,MAAM;AAGlB8J,SAAKG,SAASjE,SAASiE;AAKvB,QAAI6F,MAAMG,MAAMC,OAAO;AACrB,UAAIC,SAAS,IAAIvS,gBAAgBkM,KAAKG,MAAM;AAC5CkG,aAAOC,OAAO,OAAO;AACrBtG,WAAKG,SAASkG,OAAOE,SAAQ,IAAE,MAAOF,OAAOE,SAAQ,IAAO;IAC7D;EACF;AAED,OAAK,CAACrQ,UAAUA,WAAW,QAAQ8P,MAAMG,MAAMC,OAAO;AACpDpG,SAAKG,SAASH,KAAKG,SACfH,KAAKG,OAAOtD,QAAQ,OAAO,SAAS,IACpC;EACL;AAMD,MAAI7G,aAAa,KAAK;AACpBgK,SAAKC,WACHD,KAAKC,aAAa,MAAMjK,WAAWwQ,UAAU,CAACxQ,UAAUgK,KAAKC,QAAQ,CAAC;EACzE;AAED,SAAOuE,WAAWxE,IAAI;AACxB;AAEA,SAASyG,kBAAkBhB,YAAoBiB,SAAe;AAC5D,MAAIC,cAAoBjI,iBACtB,CAAC6D,OAAO1D,QAAO;AACb,QAAI2D,SAASgD,iBAAiBC,YAAYiB,OAAO;AACjD,WAAQnR,oBAAAmN,UAAQvL,SAAA,CAAA,GAAKoL,OAAK;MAAE1D;MAAU2D;IAAc,CAAA,CAAA;EACtD,CAAC;AAEH,MAAA3M,MAAa;AACX8Q,gBAAYxN,cAAc;EAC3B;AACD,SAAOwN;AACT;AAEA,IAAIC,YAAY;SAcAC,aAAU;AAAA,MAAAC;AACxB,MAAI;IAAE3M;EAAM,IAAK8J,qBAAqBJ,eAAekD,UAAU;AAE/D,MAAIZ,QAAc9G,iBAAW0G,YAAY;AACzC,GAAUI,QAAKtQ,OAAfsO,UAAS,OAAA,+CAAA,IAATA,UAAS,KAAA,IAAA;AAET,MAAIuC,WAAOI,iBAAGX,MAAMF,QAAQE,MAAMF,QAAQlE,SAAS,CAAC,MAAC,OAAA,SAAvC+E,eAAyCX,MAAMa;AAC7D,IACEN,WAAW,QAAI7Q,OADjBsO,UAAS,OAAA,kEAAA,IAATA,UAAS,KAAA,IAAA;AAKT,MAAI,CAACsB,UAAU,IAAUpL,eAAS,MAAM4M,OAAO,EAAEL,SAAS,CAAC;AAC3D,MAAI,CAACtE,KAAI,IAAUjI,eAAS,MAAK;AAC/B,KAAUqM,UAAO7Q,OAAjBsO,UAAS,OAAA,yCAAA,IAATA,UAAS,KAAA,IAAA;AACT,WAAOsC,kBAAkBhB,YAAYiB,OAAO;EAC9C,CAAC;AACD,MAAI,CAACQ,IAAI,IAAU7M,eAAS,MAAOuF,UAAgB;AACjD,KAAUzF,SAAMtE,OAAhBsO,UAAS,OAAS,wCAAwC,IAA1DA,UAAS,KAAA,IAAA;AACT,KAAUuC,UAAO7Q,OAAjBsO,UAAS,OAAU,yCAAyC,IAA5DA,UAAS,KAAA,IAAA;AACThK,WAAOyL,MAAMH,YAAYiB,SAAS9G,IAAI;EACxC,CAAC;AACD,MAAI4C,SAASgD,iBAAiBC,YAAYiB,OAAO;AAEjD,MAAIS,UAAUhN,OAAOiN,WAAkB3B,UAAU;AAEjD,MAAI4B,wBAA8BjL,cAChC,MAAAjF,SAAA;IACEmL,MAAAA;IACAE;IACA0E;EAAI,GACDC,OAAO,GAEZ,CAACA,SAAS7E,OAAME,QAAQ0E,IAAI,CAAC;AAG/B5N,EAAMwC,gBAAU,MAAK;AAInB,WAAO,MAAK;AACV,UAAI,CAAC3B,QAAQ;AACXmN,gBAAQC,KAAI,mDAAoD;AAChE;MACD;AACDpN,aAAOqN,cAAc/B,UAAU;;EAEnC,GAAG,CAACtL,QAAQsL,UAAU,CAAC;AAEvB,SAAO4B;AACT;SAMgBI,cAAW;AACzB,MAAI3P,QAAQsM,mBAAmBN,oBAAoB4D,WAAW;AAC9D,SAAO,CAAC,GAAG5P,MAAM6P,SAASC,OAAM,CAAE;AACpC;AAEA,IAAMC,iCAAiC;AACvC,IAAIC,uBAA+C,CAAA;AAKnD,SAASlE,qBAAoBmE,QAMvB;AAAA,MANwB;IAC5BrE;IACAC;0BAIE,CAAA,IAAEoE;AACJ,MAAI;IAAE5N;EAAM,IAAK8J,qBAAqBJ,eAAemE,oBAAoB;AACzE,MAAI;IAAEC;IAAuBrL;EAAoB,IAAGwH,mBAClDN,oBAAoBkE,oBAAoB;AAE1C,MAAI;IAAEhS;EAAQ,IAAWqJ,iBAAWC,iBAAiB;AACrD,MAAIpD,WAAWoF,YAAW;AAC1B,MAAI2E,UAAUiC,WAAU;AACxB,MAAItG,aAAauG,cAAa;AAG9B7O,EAAMwC,gBAAU,MAAK;AACnBvE,WAAOF,QAAQ+Q,oBAAoB;AACnC,WAAO,MAAK;AACV7Q,aAAOF,QAAQ+Q,oBAAoB;;KAEpC,CAAA,CAAE;AAGLC,cACQnN,kBAAY,MAAK;AACrB,QAAI0G,WAAW9J,UAAU,QAAQ;AAC/B,UAAIzD,OAAOqP,SAASA,OAAOxH,UAAU+J,OAAO,IAAI,SAAS/J,SAAS7H;AAClEyT,2BAAqBzT,GAAG,IAAIkD,OAAO+Q;IACpC;AACD,QAAI;AACFC,qBAAeC,QACb7E,cAAckE,gCACdY,KAAKC,UAAUZ,oBAAoB,CAAC;aAE/BjP,OAAO;AACdhD,aAAAC,QACE,OAAK,sGAC+F+C,QAAK,IAAI,IAC9G;IACF;AACDtB,WAAOF,QAAQ+Q,oBAAoB;EACrC,GAAG,CAACzE,YAAYD,QAAQ9B,WAAW9J,OAAOoE,UAAU+J,OAAO,CAAC,CAAC;AAI/D,MAAI,OAAO3Q,aAAa,aAAa;AAEnCgE,IAAMsC,sBAAgB,MAAK;AACzB,UAAI;AACF,YAAI+M,mBAAmBJ,eAAeK,QACpCjF,cAAckE,8BAA8B;AAE9C,YAAIc,kBAAkB;AACpBb,iCAAuBW,KAAKI,MAAMF,gBAAgB;QACnD;eACMnT,GAAG;MACV;IAEJ,GAAG,CAACmO,UAAU,CAAC;AAIfrK,IAAMsC,sBAAgB,MAAK;AACzB,UAAIkN,wBACFpF,UAAU1N,aAAa,MACnB,CAACkG,WAAU+J,aACTvC;;QACEvM,SAAA,CAAA,GAEK+E,WAAQ;UACX+D,UACE1J,cAAc2F,UAAS+D,UAAUjK,QAAQ,KACzCkG,UAAS+D;SAEbgG;QAAAA;MAAO,IAEXvC;AACN,UAAIqF,2BAA2B5O,UAAAA,OAAAA,SAAAA,OAAQ6O,wBACrClB,sBACA,MAAMvQ,OAAO+Q,SACbQ,qBAAqB;AAEvB,aAAO,MAAMC,4BAA4BA,yBAAwB;OAChE,CAAC5O,QAAQnE,UAAU0N,MAAM,CAAC;AAI7BpK,IAAMsC,sBAAgB,MAAK;AAEzB,UAAIqM,0BAA0B,OAAO;AACnC;MACD;AAGD,UAAI,OAAOA,0BAA0B,UAAU;AAC7C1Q,eAAO0R,SAAS,GAAGhB,qBAAqB;AACxC;MACD;AAGD,UAAI/L,SAASkE,MAAM;AACjB,YAAI8I,KAAK5T,SAAS6T,eAChBC,mBAAmBlN,SAASkE,KAAK8F,MAAM,CAAC,CAAC,CAAC;AAE5C,YAAIgD,IAAI;AACNA,aAAGG,eAAc;AACjB;QACD;MACF;AAGD,UAAIzM,uBAAuB,MAAM;AAC/B;MACD;AAGDrF,aAAO0R,SAAS,GAAG,CAAC;OACnB,CAAC/M,UAAU+L,uBAAuBrL,kBAAkB,CAAC;EACzD;AACH;AAYgB,SAAA0M,gBACdC,UACAlE,SAA+B;AAE/B,MAAI;IAAEmE;EAAO,IAAKnE,WAAW,CAAA;AAC7B/L,EAAMwC,gBAAU,MAAK;AACnB,QAAI9E,OAAOwS,WAAW,OAAO;MAAEA;IAAS,IAAG3S;AAC3CU,WAAOkS,iBAAiB,gBAAgBF,UAAUvS,IAAI;AACtD,WAAO,MAAK;AACVO,aAAOmS,oBAAoB,gBAAgBH,UAAUvS,IAAI;;EAE7D,GAAG,CAACuS,UAAUC,OAAO,CAAC;AACxB;AAUA,SAASnB,YACPkB,UACAlE,SAA+B;AAE/B,MAAI;IAAEmE;EAAO,IAAKnE,WAAW,CAAA;AAC7B/L,EAAMwC,gBAAU,MAAK;AACnB,QAAI9E,OAAOwS,WAAW,OAAO;MAAEA;IAAS,IAAG3S;AAC3CU,WAAOkS,iBAAiB,YAAYF,UAAUvS,IAAI;AAClD,WAAO,MAAK;AACVO,aAAOmS,oBAAoB,YAAYH,UAAUvS,IAAI;;EAEzD,GAAG,CAACuS,UAAUC,OAAO,CAAC;AACxB;AAUA,SAASG,UAASC,QAAsD;AAAA,MAArD;IAAEC;IAAM/Q;EAA6C,IAAA8Q;AACtE,MAAIE,UAAUC,WAAWF,IAAI;AAE7BvQ,EAAMwC,gBAAU,MAAK;AACnB,QAAIgO,QAAQhS,UAAU,WAAW;AAC/B,UAAIkS,UAAUzS,OAAO0S,QAAQnR,OAAO;AACpC,UAAIkR,SAAS;AAIXE,mBAAWJ,QAAQE,SAAS,CAAC;MAC9B,OAAM;AACLF,gBAAQK,MAAK;MACd;IACF;EACH,GAAG,CAACL,SAAShR,OAAO,CAAC;AAErBQ,EAAMwC,gBAAU,MAAK;AACnB,QAAIgO,QAAQhS,UAAU,aAAa,CAAC+R,MAAM;AACxCC,cAAQK,MAAK;IACd;EACH,GAAG,CAACL,SAASD,IAAI,CAAC;AACpB;AAYA,SAASrI,uBACP7E,IACA3F,MAA6C;AAAA,MAA7CA,SAAAA,QAAAA;AAAAA,WAA2C,CAAA;EAAE;AAE7C,MAAIwD,YAAkB6E,iBAAWrG,qBAAqB;AAEtD,IACEwB,aAAa,QAAI3E,OADnBsO,UAEE,OAAA,iKACqE,IAHvEA,UAAS,KAAA,IAAA;AAMT,MAAI;IAAEnO;EAAQ,IAAKiO,qBACjBJ,eAAerC,sBAAsB;AAEvC,MAAIxB,OAAOqB,gBAAgB1E,IAAI;IAAEoC,UAAU/H,KAAK+H;EAAQ,CAAE;AAC1D,MAAI,CAACvE,UAAUtB,iBAAiB;AAC9B,WAAO;EACR;AAED,MAAIkR,cACF7T,cAAciE,UAAUkB,gBAAgBuE,UAAUjK,QAAQ,KAC1DwE,UAAUkB,gBAAgBuE;AAC5B,MAAIoK,WACF9T,cAAciE,UAAUmB,aAAasE,UAAUjK,QAAQ,KACvDwE,UAAUmB,aAAasE;AAezB,SACEqK,UAAUtK,KAAKC,UAAUoK,QAAQ,KAAK,QACtCC,UAAUtK,KAAKC,UAAUmK,WAAW,KAAK;AAE7C;", "names": ["defaultMethod", "defaultEncType", "isHtmlElement", "object", "tagName", "isButtonElement", "toLowerCase", "isFormElement", "isInputElement", "isModifiedEvent", "event", "metaKey", "altKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "shouldProcessLinkClick", "target", "button", "createSearchParams", "init", "URLSearchParams", "Array", "isArray", "Object", "keys", "reduce", "memo", "key", "value", "concat", "map", "v", "getSearchParamsForLocation", "locationSearch", "defaultSearchParams", "searchParams", "for<PERSON>ach", "_", "has", "getAll", "append", "_formDataSupportsSubmitter", "isFormDataSubmitterSupported", "FormData", "document", "createElement", "e", "supportedFormEncTypes", "Set", "getFormEncType", "encType", "process", "warning", "getFormSubmissionInfo", "basename", "method", "action", "formData", "body", "attr", "getAttribute", "stripBasename", "type", "form", "Error", "name", "prefix", "undefined", "createBrowserRouter", "routes", "opts", "createRouter", "future", "_extends", "v7_prependBasename", "history", "createBrowserHistory", "window", "hydrationData", "parseHydrationData", "initialize", "createHashRouter", "createHashHistory", "_window", "state", "__staticRouterHydrationData", "errors", "deserializeErrors", "entries", "serialized", "val", "__type", "ErrorResponseImpl", "status", "statusText", "data", "internal", "__subType", "ErrorConstructor", "error", "message", "stack", "ViewTransitionContext", "createContext", "isTransitioning", "displayName", "START_TRANSITION", "startTransitionImpl", "React", "startTransitionSafe", "cb", "Deferred", "constructor", "promise", "Promise", "resolve", "reject", "reason", "RouterProvider", "_ref", "fallbackElement", "router", "setStateImpl", "useState", "pendingState", "setPendingState", "vtContext", "setVtContext", "renderDfd", "setRenderDfd", "transition", "setTransition", "interruption", "setInterruption", "v7_startTransition", "optInStartTransition", "useCallback", "setState", "newState", "_ref2", "unstable_viewTransitionOpts", "viewTransitionOpts", "startViewTransition", "skipTransition", "currentLocation", "nextLocation", "useLayoutEffect", "subscribe", "useEffect", "renderPromise", "finished", "finally", "location", "navigator", "useMemo", "createHref", "encodeLocation", "go", "n", "navigate", "push", "to", "preventScrollReset", "replace", "dataRouterContext", "static", "Fragment", "DataRouterContext", "Provider", "DataRouterStateContext", "Router", "navigationType", "historyAction", "initialized", "DataRoutes", "_ref3", "useRoutesImpl", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref4", "children", "historyRef", "useRef", "current", "v5Compat", "listen", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref5", "HistoryRouter", "_ref6", "<PERSON><PERSON><PERSON><PERSON>", "ABSOLUTE_URL_REGEX", "Link", "forwardRef", "LinkWithRef", "_ref7", "ref", "onClick", "relative", "reloadDocument", "unstable_viewTransition", "rest", "_objectWithoutPropertiesLoose", "_excluded", "useContext", "NavigationContext", "absoluteHref", "isExternal", "test", "currentUrl", "URL", "href", "targetUrl", "startsWith", "protocol", "path", "pathname", "origin", "search", "hash", "useHref", "internalOnClick", "useLinkClickHandler", "handleClick", "defaultPrevented", "NavLink", "NavLinkWithRef", "_ref8", "ariaCurrentProp", "caseSensitive", "className", "classNameProp", "end", "style", "styleProp", "_excluded2", "useResolvedPath", "useLocation", "routerState", "useViewTransitionState", "toPathname", "locationPathname", "nextLocationPathname", "navigation", "isActive", "char<PERSON>t", "length", "isPending", "renderProps", "aria<PERSON>urrent", "filter", "Boolean", "join", "Form", "props", "submit", "useSubmit", "FormImpl", "_ref9", "forwardedRef", "onSubmit", "_excluded3", "formMethod", "formAction", "useFormAction", "<PERSON><PERSON><PERSON><PERSON>", "preventDefault", "submitter", "nativeEvent", "submitMethod", "currentTarget", "ScrollRestoration", "_ref10", "<PERSON><PERSON><PERSON>", "storageKey", "useScrollRestoration", "DataRouterHook", "DataRouterStateHook", "getDataRouterConsoleError", "<PERSON><PERSON><PERSON>", "useDataRouterContext", "ctx", "invariant", "useDataRouterState", "_temp", "replaceProp", "useNavigate", "createPath", "useSearchParams", "defaultInit", "defaultSearchParamsRef", "hasSetSearchParamsRef", "setSearchParams", "nextInit", "navigateOptions", "newSearchParams", "validateClientSideSubmission", "UseSubmit", "currentRouteId", "useRouteId", "options", "formEncType", "fromRouteId", "useSubmitFetcher", "fetcher<PERSON>ey", "fetcherRouteId", "UseSubmitFetcher", "fetch", "_temp2", "routeContext", "RouteContext", "match", "matches", "slice", "route", "index", "params", "delete", "toString", "joinPaths", "createFetcherForm", "routeId", "FetcherForm", "fetcherId", "useFetcher", "_route$matches", "UseFetcher", "id", "String", "load", "fetcher", "getFetcher", "fetcherWithComponents", "console", "warn", "deleteFetcher", "useFetchers", "UseFetchers", "fetchers", "values", "SCROLL_RESTORATION_STORAGE_KEY", "savedScrollPositions", "_temp3", "UseScrollRestoration", "restoreScrollPosition", "useMatches", "useNavigation", "scrollRestoration", "usePageHide", "scrollY", "sessionStorage", "setItem", "JSON", "stringify", "sessionPositions", "getItem", "parse", "getKeyWithoutBasename", "disableScrollRestoration", "enableScrollRestoration", "scrollTo", "el", "getElementById", "decodeURIComponent", "scrollIntoView", "useBeforeUnload", "callback", "capture", "addEventListener", "removeEventListener", "usePrompt", "_ref11", "when", "blocker", "useBlocker", "proceed", "confirm", "setTimeout", "reset", "currentPath", "nextPath", "matchPath"]}