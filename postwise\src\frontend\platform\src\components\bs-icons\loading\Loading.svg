<svg viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg" width="80" height="80">
    <!-- 主体P形状 -->
    <path d="M10 8v20h4V19h6c3.3 0 6-2.7 6-5.5S23.3 8 20 8H10z" stroke="currentColor" stroke-width="2" fill="none">
        <animate attributeName="stroke-dasharray" values="0,100;100,0" dur="1.2s" repeatCount="indefinite" />
    </path>
    
    <!-- 内部弧线 -->
    <path d="M14 12h5c1.7 0 3 1.3 3 3s-1.3 3-3 3h-5v-6z" fill="none" stroke="currentColor" stroke-width="1.5">
        <animate attributeName="stroke-dasharray" values="0,60;60,0" dur="1.2s" begin="0.4s" repeatCount="indefinite" />
    </path>
    
    <!-- 点缀效果 -->
    <circle cx="22" cy="11" r="1.5" fill="currentColor">
        <animate attributeName="opacity" values="0.3;1;0.3" dur="1s" repeatCount="indefinite" begin="0.2s" />
    </circle>
</svg>