<!DOCTYPE html>
<html lang="en-US">

<head>
  <meta charset="utf-8" />
  <meta name="theme-color" content="#171717" />
  <meta name="mobile-web-app-capable" content="yes" />
  <meta name="apple-mobile-web-app-capable" content="yes" />
  <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
  <meta name="description" content="Deepseek - An open source chat application with support for multiple AI models" />
  <title>POSTWISE</title>
  <meta name="viewport" content="width=device-width, initial-scale=1, interactive-widget=resizes-content" />
  <style>
    html,
    body {
      margin: 0;
      padding: 0;
      height: 100%;
    }
  </style>
  <!-- <script src="http://*************:9000/vditor.js" defer></script> -->
  <script>
    const theme = localStorage.getItem('color-theme');
    const loadingContainerStyle = document.createElement('style');
    let backgroundColor;

    if (theme === 'dark') {
      backgroundColor = '#0d0d0d';
    } else if (theme === 'light') {
      backgroundColor = '#ffffff';
    } else if (theme === 'system') {
      const prefersDarkScheme = window.matchMedia('(prefers-color-scheme: dark)').matches;
      backgroundColor = prefersDarkScheme ? '#0d0d0d' : '#ffffff';
    } else {
      backgroundColor = '#ffffff';
    }

    loadingContainerStyle.innerHTML = `
        #loading-container {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 100vh;
          background-color: ${backgroundColor};
        }
      `;
    document.head.appendChild(loadingContainerStyle);
  </script>
  <script defer type="module" src="/src/main.jsx"></script>
</head>

<body>
  <div id="root">
    <div id="loading-container"></div>
  </div>
</body>
<script defer src="/case/case.js"></script>
</html>