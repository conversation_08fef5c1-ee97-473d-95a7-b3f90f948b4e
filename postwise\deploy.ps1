# Postwise Deployment Script
Write-Host "Postwise Deployment Script" -ForegroundColor Cyan
Write-Host "================================" -ForegroundColor Cyan

# Get version number
do {
    $VERSION = Read-Host "Please enter version number (e.g., 2.0.1)"
    if ([string]::IsNullOrWhiteSpace($VERSION)) {
        Write-Host "Version number cannot be empty" -ForegroundColor Red
    }
} while ([string]::IsNullOrWhiteSpace($VERSION))

# Select build option
Write-Host ""
Write-Host "Please select build option:" -ForegroundColor Yellow
Write-Host "   1. Build frontend only" -ForegroundColor White
Write-Host "   2. Build backend only" -ForegroundColor White
Write-Host "   3. Build both frontend and backend" -ForegroundColor White
Write-Host "   4. Skip build, restart services only" -ForegroundColor White
Write-Host ""

do {
    $BUILD_OPTION = Read-Host "Please select (1-4)"
    if ([string]::IsNullOrWhiteSpace($BUILD_OPTION)) {
        $BUILD_OPTION = "3"
    }
    
    switch ($BUILD_OPTION) {
        "1" { $BUILD_TYPE = "frontend"; break }
        "2" { $BUILD_TYPE = "backend"; break }
        "3" { $BUILD_TYPE = "both"; break }
        "4" { $BUILD_TYPE = "none"; break }
        default { 
            Write-Host "Invalid selection, please enter 1-4" -ForegroundColor Red
            $BUILD_TYPE = $null
        }
    }
} while ($null -eq $BUILD_TYPE)

# Configuration variables
$SERVER = "*************"
$USERNAME = "root"

Write-Host ""
Write-Host "Deployment Info:" -ForegroundColor Cyan
Write-Host "   Version: v$VERSION" -ForegroundColor White
Write-Host "   Server: $SERVER" -ForegroundColor White
Write-Host "   Build Type: $BUILD_TYPE" -ForegroundColor White
Write-Host ""

# Check SSH connection
Write-Host "Checking SSH connection..." -ForegroundColor Yellow
ssh $USERNAME@$SERVER 'echo SSH connection OK' 2>$null | Out-Null
if ($LASTEXITCODE -ne 0) {
    Write-Host "SSH connection failed" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}
Write-Host "SSH connection OK" -ForegroundColor Green

# Pull latest code
Write-Host ""
Write-Host "Pulling latest code..." -ForegroundColor Yellow
ssh $USERNAME@$SERVER 'cd /home/<USER>'
if ($LASTEXITCODE -ne 0) {
    Write-Host "Code pull failed" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}
Write-Host "Code pull completed" -ForegroundColor Green

# Build images
Write-Host ""
if ($BUILD_TYPE -eq "backend" -or $BUILD_TYPE -eq "both") {
    Write-Host "Building backend image..." -ForegroundColor Yellow
    $backendCmd = "cd /home/<USER>/src/backend && docker buildx build --load -t postwise-backend:v$VERSION ."
    ssh $USERNAME@$SERVER $backendCmd
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Backend image build failed" -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
    Write-Host "Backend image build completed" -ForegroundColor Green
}

if ($BUILD_TYPE -eq "frontend" -or $BUILD_TYPE -eq "both") {
    Write-Host ""
    Write-Host "Building frontend image..." -ForegroundColor Yellow
    $frontendCmd = "cd /home/<USER>/src/frontend && docker buildx build --load -t postwise-frontend:v$VERSION ."
    ssh $USERNAME@$SERVER $frontendCmd
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Frontend image build failed" -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
    Write-Host "Frontend image build completed" -ForegroundColor Green
}

if ($BUILD_TYPE -eq "none") {
    Write-Host "Skipping build step" -ForegroundColor Cyan
}

# Update docker-compose.yml version numbers
if ($BUILD_TYPE -ne "none") {
    Write-Host ""
    Write-Host "Updating docker-compose.yml version numbers..." -ForegroundColor Yellow

    if ($BUILD_TYPE -eq "backend" -or $BUILD_TYPE -eq "both") {
        $backendSedCmd = "cd /home/<USER>/docker && sed -i 's/postwise-backend:v[0-9]\+\.[0-9]\+\.[0-9]\+/postwise-backend:v$VERSION/g' docker-compose.yml"
        ssh $USERNAME@$SERVER $backendSedCmd
    }
    if ($BUILD_TYPE -eq "frontend" -or $BUILD_TYPE -eq "both") {
        $frontendSedCmd = "cd /home/<USER>/docker && sed -i 's/postwise-frontend:v[0-9]\+\.[0-9]\+\.[0-9]\+/postwise-frontend:v$VERSION/g' docker-compose.yml"
        ssh $USERNAME@$SERVER $frontendSedCmd
    }

    if ($LASTEXITCODE -ne 0) {
        Write-Host "Version number update failed" -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
    Write-Host "Version number update completed" -ForegroundColor Green
}

# Stop existing services
Write-Host ""
Write-Host "Stopping existing services..." -ForegroundColor Yellow
ssh $USERNAME@$SERVER 'cd /home/<USER>/docker && docker-compose down'

# Start services
Write-Host ""
Write-Host "Starting services..." -ForegroundColor Yellow
ssh $USERNAME@$SERVER 'cd /home/<USER>/docker && docker-compose up -d'
if ($LASTEXITCODE -ne 0) {
    Write-Host "Service startup failed" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}
Write-Host "Service startup completed" -ForegroundColor Green

# Wait for services to start
Write-Host ""
Write-Host "Waiting for services to start..." -ForegroundColor Yellow
Start-Sleep -Seconds 30

# Check service status
Write-Host ""
Write-Host "Checking service status..." -ForegroundColor Yellow
ssh $USERNAME@$SERVER 'cd /home/<USER>/docker && docker-compose ps'

# Deployment completed
Write-Host ""
Write-Host "================================" -ForegroundColor Cyan
Write-Host "Deployment completed!" -ForegroundColor Green
Write-Host "Access URLs:" -ForegroundColor Cyan
Write-Host "   Backend: http://*************:7860" -ForegroundColor White
Write-Host "   Frontend: http://*************:3001" -ForegroundColor White
Write-Host "   Version: v$VERSION" -ForegroundColor White
Write-Host "   Time: $(Get-Date)" -ForegroundColor White
Write-Host "================================" -ForegroundColor Cyan

# Log deployment history
$logEntry = "$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss'): Deployed version v$VERSION ($BUILD_TYPE) to $SERVER - Success"
Add-Content -Path "deploy-history.log" -Value $logEntry

Read-Host "Press Enter to exit"
