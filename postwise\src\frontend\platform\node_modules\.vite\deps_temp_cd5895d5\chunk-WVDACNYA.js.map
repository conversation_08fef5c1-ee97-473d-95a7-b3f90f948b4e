{"version": 3, "sources": ["../../refractor/lang/dns-zone-file.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = dnsZoneFile\ndnsZoneFile.displayName = 'dnsZoneFile'\ndnsZoneFile.aliases = []\nfunction dnsZoneFile(Prism) {\n  Prism.languages['dns-zone-file'] = {\n    comment: /;.*/,\n    string: {\n      pattern: /\"(?:\\\\.|[^\"\\\\\\r\\n])*\"/,\n      greedy: true\n    },\n    variable: [\n      {\n        pattern: /(^\\$ORIGIN[ \\t]+)\\S+/m,\n        lookbehind: true\n      },\n      {\n        pattern: /(^|\\s)@(?=\\s|$)/,\n        lookbehind: true\n      }\n    ],\n    keyword: /^\\$(?:INCLUDE|ORIGIN|TTL)(?=\\s|$)/m,\n    class: {\n      // https://tools.ietf.org/html/rfc1035#page-13\n      pattern: /(^|\\s)(?:CH|CS|HS|IN)(?=\\s|$)/,\n      lookbehind: true,\n      alias: 'keyword'\n    },\n    type: {\n      // https://en.wikipedia.org/wiki/List_of_DNS_record_types\n      pattern:\n        /(^|\\s)(?:A|A6|AAAA|AFSDB|APL|ATMA|CAA|CDNSKEY|CDS|CERT|CNAME|DHCID|DLV|DNAME|DNSKEY|DS|EID|GID|GPOS|HINFO|HIP|IPSECKEY|ISDN|KEY|KX|LOC|MAILA|MAILB|MB|MD|MF|MG|MINFO|MR|MX|NAPTR|NB|NBSTAT|NIMLOC|NINFO|NS|NSAP|NSAP-PTR|NSEC|NSEC3|NSEC3PARAM|NULL|NXT|OPENPGPKEY|PTR|PX|RKEY|RP|RRSIG|RT|SIG|SINK|SMIMEA|SOA|SPF|SRV|SSHFP|TA|TKEY|TLSA|TSIG|TXT|UID|UINFO|UNSPEC|URI|WKS|X25)(?=\\s|$)/,\n      lookbehind: true,\n      alias: 'keyword'\n    },\n    punctuation: /[()]/\n  }\n  Prism.languages['dns-zone'] = Prism.languages['dns-zone-file']\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,gBAAY,cAAc;AAC1B,gBAAY,UAAU,CAAC;AACvB,aAAS,YAAY,OAAO;AAC1B,YAAM,UAAU,eAAe,IAAI;AAAA,QACjC,SAAS;AAAA,QACT,QAAQ;AAAA,UACN,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA,QACA,UAAU;AAAA,UACR;AAAA,YACE,SAAS;AAAA,YACT,YAAY;AAAA,UACd;AAAA,UACA;AAAA,YACE,SAAS;AAAA,YACT,YAAY;AAAA,UACd;AAAA,QACF;AAAA,QACA,SAAS;AAAA,QACT,OAAO;AAAA;AAAA,UAEL,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,OAAO;AAAA,QACT;AAAA,QACA,MAAM;AAAA;AAAA,UAEJ,SACE;AAAA,UACF,YAAY;AAAA,UACZ,OAAO;AAAA,QACT;AAAA,QACA,aAAa;AAAA,MACf;AACA,YAAM,UAAU,UAAU,IAAI,MAAM,UAAU,eAAe;AAAA,IAC/D;AAAA;AAAA;", "names": []}