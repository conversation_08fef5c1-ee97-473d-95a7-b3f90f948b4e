<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="eb9cff37-63d0-4527-9d8d-ef435acd7e47" name="更改" comment="feat(deploy): 添加 main 分支下， Docker 部署 postwise 的脚本文件。&#10;&#10;- 支持选择性构建【前端】和【后端】镜像&#10;- 自动更新 docker-compose.yml 版本号（暂时默认v2.0.0）&#10;- 部署过程中检查 SSH 连接并拉取最新代码&#10;- 部署完成后检查服务状态并记录部署日志" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="dev-shuilang" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 1
}</component>
  <component name="ProjectId" id="31GYmITnnSFNqfXqkpjubub13BX" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Python.main.executor&quot;: &quot;Run&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/projects/postwise-v2.0/postwise&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;Docker&quot;,
    &quot;ts.external.directory.path&quot;: &quot;D:\\projects\\postwise-v2.0\\postwise\\src\\frontend\\platform\\node_modules\\typescript\\lib&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RunManager">
    <configuration name="main" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="postwise" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/src/backend/bisheng" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/src/backend/bisheng/main.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Python.main" />
        <item itemvalue="Python.main" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-1d06a55b98c1-0b3e54e931b4-JavaScript-PY-241.17011.127" />
        <option value="bundled-python-sdk-48aec45f0201-7e9c3bbb6e34-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-241.17011.127" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="eb9cff37-63d0-4527-9d8d-ef435acd7e47" name="更改" comment="" />
      <created>1755152034140</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1755152034140</updated>
      <workItem from="1755152035546" duration="10674000" />
      <workItem from="1755239075568" duration="149000" />
      <workItem from="1755240156713" duration="20000" />
      <workItem from="1755242668346" duration="32000" />
      <workItem from="1755243830491" duration="2031000" />
      <workItem from="1755194685557" duration="1726000" />
    </task>
    <task id="LOCAL-00001" summary="feat: 更新第一版的 v2.0.0 &#10;&#10;- 新增 .drone.yml 文件，配置 Docker 和 SSH 部署步骤&#10;- 添加多个 .gitignore 文件，用于不同目录的文件忽略&#10;- 新增 .gitmodules 文件，定义子模块路径和 URL- 添加 .pre-commit-config.yaml 文件，配置代码提交前的检查规则&#10;- 新增多个 Markdown 文件，记录股东结构、注册资本等信息">
      <option name="closed" value="true" />
      <created>1755152880048</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1755152880050</updated>
    </task>
    <task id="LOCAL-00002" summary="fix: 修复启动问题；同时移除不需要的依赖。&#10;&#10;1. （最主要的）修复了 fastapi-jwt-auth 与 pydantic v2 的兼容性问题，即手动修补了 config.py 文件，在我的电脑上的位置是：&#10;【C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\fastapi_jwt_auth\config.py】&#10;&#10;具体的代码层面：&#10;1. 类型注解：Sequence[StrictStr] → List[StrictStr]&#10;2. Validator 装饰器：移除 each_item=True 参数&#10;3. Validator 逻辑：手动添加 isinstance() 检查和 for 循环来验证每个项目&#10;4. 配置类：min_anystr_length → str_min_length，anystr_strip_whitespace → str_strip_whitespace&#10;&#10;2. 修复了 pyproject.toml 配置问题：&#10;- 将过时的 [tool.poetry.dev-dependencies] 更新为 [tool.poetry.group.dev.dependencies]&#10;&#10;- 移除了有问题的 langchain-serve extra 依赖">
      <option name="closed" value="true" />
      <created>1755157522387</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1755157522387</updated>
    </task>
    <task id="LOCAL-00003" summary="docs(backend): 更新 README 文档以包含后端本地开发指南&#10;&#10;- 新增后端配置文件说明，指导如何修改 MySQL、Redis、Milvus、ES 和 MinIO 配置&#10;- 添加下载依赖和启动项目的详细步骤说明&#10;- 更新项目名称为&quot;邮智&quot;，替换原&quot;毕昇&quot;名称">
      <option name="closed" value="true" />
      <created>1755157855394</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1755157855394</updated>
    </task>
    <task id="LOCAL-00004" summary="build：修改官方的后端Dockerfile文件，改成公司镜像，添加国内镜像源。&#10;&#10;- 利用 Docker 缓存通过首先复制依赖文件&#10;- 配置 poetry 使用国内镜像源以加速下载&#10;- 优化补丁应用过程&#10;- 添加执行权限设置以确保脚本可执行">
      <option name="closed" value="true" />
      <created>1755158426718</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1755158426718</updated>
    </task>
    <task id="LOCAL-00005" summary="docs(backend): 补充 fastapi-jwt-auth 启动报错解决方案&#10;&#10;- 新增解决方案文档，详细说明 fastapi-jwt-auth 的修复方法&#10;- 在 README 中添加报错提示和解决方案文档的引用">
      <option name="closed" value="true" />
      <created>1755158604387</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1755158604387</updated>
    </task>
    <task id="LOCAL-00006" summary="build(frontend): 更新 Dockerfile以使用私有镜像仓库&#10;&#10;- 将 node 和 nginx镜像来源从官方更改为私有镜像仓库 reg.un-net.com&#10;- 保留了原有的构建逻辑和配置">
      <option name="closed" value="true" />
      <created>1755160233600</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1755160233600</updated>
    </task>
    <task id="LOCAL-00007" summary="chore: 添加启动应用服务器、Celery Worker和前端应用的批处理脚本&#10;&#10;- 新增 start_app.cmd、start_celery.cmd 和 start_frontend.cmd 文件&#10;- 实现了启动不同服务的自动化脚本&#10;- 增加了目录存在性检查和错误提示功能&#10;- 优化了启动命令的显示和说明">
      <option name="closed" value="true" />
      <created>1755160256346</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1755160256346</updated>
    </task>
    <task id="LOCAL-00008" summary="feat(deploy): 添加 main 分支下， Docker 部署 postwise 的脚本文件。&#10;&#10;- 支持选择性构建【前端】和【后端】镜像&#10;- 自动更新 docker-compose.yml 版本号（暂时默认v2.0.0）&#10;- 部署过程中检查 SSH 连接并拉取最新代码&#10;- 部署完成后检查服务状态并记录部署日志">
      <option name="closed" value="true" />
      <created>1755248215974</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1755248215974</updated>
    </task>
    <option name="localTasksCounter" value="9" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CHECK_NEW_TODO" value="false" />
    <MESSAGE value="feat: 更新第一版的 v2.0.0 &#10;&#10;- 新增 .drone.yml 文件，配置 Docker 和 SSH 部署步骤&#10;- 添加多个 .gitignore 文件，用于不同目录的文件忽略&#10;- 新增 .gitmodules 文件，定义子模块路径和 URL- 添加 .pre-commit-config.yaml 文件，配置代码提交前的检查规则&#10;- 新增多个 Markdown 文件，记录股东结构、注册资本等信息" />
    <MESSAGE value="fix: 修复启动问题；同时移除不需要的依赖。&#10;&#10;1. （最主要的）修复了 fastapi-jwt-auth 与 pydantic v2 的兼容性问题，即手动修补了 config.py 文件，在我的电脑上的位置是：&#10;【C:\Users\<USER>\AppData\Local\Programs\Python\Python310\lib\site-packages\fastapi_jwt_auth\config.py】&#10;&#10;具体的代码层面：&#10;1. 类型注解：Sequence[StrictStr] → List[StrictStr]&#10;2. Validator 装饰器：移除 each_item=True 参数&#10;3. Validator 逻辑：手动添加 isinstance() 检查和 for 循环来验证每个项目&#10;4. 配置类：min_anystr_length → str_min_length，anystr_strip_whitespace → str_strip_whitespace&#10;&#10;2. 修复了 pyproject.toml 配置问题：&#10;- 将过时的 [tool.poetry.dev-dependencies] 更新为 [tool.poetry.group.dev.dependencies]&#10;&#10;- 移除了有问题的 langchain-serve extra 依赖" />
    <MESSAGE value="docs(backend): 更新 README 文档以包含后端本地开发指南&#10;&#10;- 新增后端配置文件说明，指导如何修改 MySQL、Redis、Milvus、ES 和 MinIO 配置&#10;- 添加下载依赖和启动项目的详细步骤说明&#10;- 更新项目名称为&quot;邮智&quot;，替换原&quot;毕昇&quot;名称" />
    <MESSAGE value="build：修改官方的后端Dockerfile文件，改成公司镜像，添加国内镜像源。&#10;&#10;- 利用 Docker 缓存通过首先复制依赖文件&#10;- 配置 poetry 使用国内镜像源以加速下载&#10;- 优化补丁应用过程&#10;- 添加执行权限设置以确保脚本可执行" />
    <MESSAGE value="docs(backend): 补充 fastapi-jwt-auth 启动报错解决方案&#10;&#10;- 新增解决方案文档，详细说明 fastapi-jwt-auth 的修复方法&#10;- 在 README 中添加报错提示和解决方案文档的引用" />
    <MESSAGE value="build(frontend): 更新 Dockerfile以使用私有镜像仓库&#10;&#10;- 将 node 和 nginx镜像来源从官方更改为私有镜像仓库 reg.un-net.com&#10;- 保留了原有的构建逻辑和配置" />
    <MESSAGE value="chore: 添加启动应用服务器、Celery Worker和前端应用的批处理脚本&#10;&#10;- 新增 start_app.cmd、start_celery.cmd 和 start_frontend.cmd 文件&#10;- 实现了启动不同服务的自动化脚本&#10;- 增加了目录存在性检查和错误提示功能&#10;- 优化了启动命令的显示和说明" />
    <MESSAGE value="feat(deploy): 添加 main 分支下， Docker 部署 postwise 的脚本文件。&#10;&#10;- 支持选择性构建【前端】和【后端】镜像&#10;- 自动更新 docker-compose.yml 版本号（暂时默认v2.0.0）&#10;- 部署过程中检查 SSH 连接并拉取最新代码&#10;- 部署完成后检查服务状态并记录部署日志" />
    <option name="LAST_COMMIT_MESSAGE" value="feat(deploy): 添加 main 分支下， Docker 部署 postwise 的脚本文件。&#10;&#10;- 支持选择性构建【前端】和【后端】镜像&#10;- 自动更新 docker-compose.yml 版本号（暂时默认v2.0.0）&#10;- 部署过程中检查 SSH 连接并拉取最新代码&#10;- 部署完成后检查服务状态并记录部署日志" />
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/postwise$main.coverage" NAME="main 覆盖结果" MODIFIED="1755160015355" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/src/backend/bisheng" />
  </component>
</project>