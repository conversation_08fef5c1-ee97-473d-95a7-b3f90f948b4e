Stack trace:
Frame         Function      Args
0007FFFF9B70  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFF9B70, 0007FFFF8A70) msys-2.0.dll+0x1FE8E
0007FFFF9B70  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF9E48) msys-2.0.dll+0x67F9
0007FFFF9B70  000210046832 (000210286019, 0007FFFF9A28, 0007FFFF9B70, 000000000000) msys-2.0.dll+0x6832
0007FFFF9B70  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF9B70  000210068E24 (0007FFFF9B80, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFF9E50  00021006A225 (0007FFFF9B80, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFFA3A60000 ntdll.dll
7FFFA23F0000 KERNEL32.DLL
7FFFA0D80000 KERNELBASE.dll
7FFF9CC00000 apphelp.dll
7FFFA3530000 USER32.dll
7FFFA1180000 win32u.dll
7FFFA23C0000 GDI32.dll
7FFFA0C40000 gdi32full.dll
7FFFA0B90000 msvcp_win.dll
7FFFA1490000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFFA2E10000 advapi32.dll
7FFFA32E0000 msvcrt.dll
7FFFA2CA0000 sechost.dll
7FFFA3410000 RPCRT4.dll
7FFFA0050000 CRYPTBASE.DLL
7FFFA11B0000 bcryptPrimitives.dll
7FFFA3700000 IMM32.DLL
7FFF12FF0000 MacType64.dll
7FFF12C90000 MacType64.Core.dll
